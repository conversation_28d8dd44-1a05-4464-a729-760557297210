**High-Level Documentation of the Provided SCSS Code**

---

### Overview

This code is an SCSS (Sass) stylesheet intended to customize and extend <PERSON><PERSON><PERSON>'s appearance, with additional emphasis on right-to-left (RTL) layout support and personalized UI components for a responsive web application.

---

### Key Features

#### 1. **Custom Variables and Bootstrap Overrides**
   - **Primary and Secondary Colors:** Sets `$primary` and `$secondary` (used throughout custom components).
   - **RTL Support:** Enables `$rtl` and provides specific styles for `[dir="rtl"]` containers.
   - **Bootstrap Options:** Overrides to enable rounded corners, shadows, and disables gradients.

#### 2. **RTL Layout Adjustments**
   - Adjusts the alignment of dropdown menus and form checks for RTL languages to ensure proper positioning and spacing.

#### 3. **Custom Component Styles**
   - **Navbar:** Applies shadow and smooth transition effects. Enhances nav-link hover states with animated underline.
   - **Primary Button:** Adds lift and shadow effects on hover for better interactivity.
   - **Event Card:** Distinct hover effect and styled image sections for event display cards.

#### 4. **Animations**
   - Defines a `fadeIn` animation and `.fade-in` class for use in various UI components.

#### 5. **Footer/Social Links**
   - Styles social media links as circular, interactive icons with hover effects in the footer section.

#### 6. **Responsive Design (Media Queries)**
   - **Small Screens (≤768px):**
     - Adjusts navbar collapse styling and event card spacing for mobile usability.
   - **Large Screens (≥992px):**
     - Adds padding to the navbar for improved spacing on larger displays.

---

### Intended Use

- To create a visually distinctive, responsive Bootstrap-based website with special handling for RTL languages.
- To provide custom and appealing UI effects (animations, shadows, interactions) and component-specific tweaks for usability, particularly for navigation, event cards, and footer elements.

---

**Summary:**  
This SCSS file customizes Bootstrap settings and component styles to ensure an attractive, user-friendly, and internationalized (RTL-ready) web design, complete with responsive behaviors and engaging interactive effects.