# Code Review Report

## General Comments  
The code is mostly well-structured and seems functional. However, several **industry standard**, **security**, and **performance** issues, as well as **possible errors/logic flaws**, were identified.  
Below is a detailed review with **line-specific corrections/recommendations**, including pseudo code for the corrected lines/sections.

---

## 1. **Error Reporting Shouldn't Be Enabled in Production**
**Issue:**  
Displaying errors with `ini_set('display_errors', 1)` and error reporting at E_ALL is a security risk in production.

**Correction:**  
```pseudo
// Pseudocode: Disable error display in production
ini_set('display_errors', 0); // in production environment
```
_Consider making error level and display conditional based on environment._

---

## 2. **Unfiltered External Input**
**Issue:**  
User-supplied values (e.g., `event_id`, `quantity`, `card_number`, etc.) are used directly with minimal sanitization. This is vulnerable to XSS, code injection, or logic errors.

**Correction:**  
```pseudo
// Pseudocode: Validate/Sanitize all external input
$event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
if ($event_id === false) { /* handle error */ }

$quantity = filter_input(INPUT_POST, 'quantity', FILTER_VALIDATE_INT) ?? 1;
$card_number = trim($_POST['card_number']); // Already cleaned later
$expiry_date = trim($_POST['expiry_date']);
$cvv = trim($_POST['cvv']);
```

---

## 3. **Expiry Date Validation Missing**
**Issue:**  
The expiry date is not validated for correct format/future date.

**Correction:**  
```pseudo
// Pseudocode: Validate expiry_date MM/YY format and check it's not expired
if (!preg_match('/^(0[1-9]|1[0-2])\/\d{2}$/', $expiry_date)) {
    // handle invalid expiry format
}
// Check if the expiry is in the future
// (split month/year, convert to date, compare to now)
```

---

## 4. **CVV Validation Missing**
**Issue:**  
CVV is not validated. Should be 3 or 4 digits, numeric.

**Correction:**  
```pseudo
// Pseudocode: Validate CVV numeric and length (typically 3 or 4)
if (!preg_match('/^\d{3,4}$/', $cvv)) {
    // handle invalid CVV
}
```

---

## 5. **Session Usage Without `session_start()`**
**Issue:**  
Session variables are used (e.g., `$_SESSION['payment_error']`) but no `session_start()`.

**Correction:**  
```pseudo
// Pseudocode: Put session_start() at the top after error config but before any session usage
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
```

---

## 6. **SQL Injection Possibility in Raw PDO Calls**
**Issue:**  
Some parts (like transport booking), data used in queries is bound positionally, but fields are not validated/sanitized, leading to a risk if code pattern changes or future developer misuses it.

**Recommendation:**  
- Strictly validate `$data` fields as above.
- For further safety, perform explicit typecasting before SQL binding.

---

## 7. **PRNG Used for Ticket & Booking Code Generation is Not Secure**
**Issue:**  
Using `md5(time() . rand())` is not cryptographically secure.

**Correction:**  
```pseudo
// (for PHP 7+)
// Use bin2hex(random_bytes(5)) or random_int(), e.g.:
$ticket_codes[] = 'TICKET_' . strtoupper(bin2hex(random_bytes(5)));
$booking_code = 'TR' . strtoupper(bin2hex(random_bytes(4)));
```

---

## 8. **Credit Card/PCI Data Storage Practice Violation**
**Issue:**  
Storing masked card number is generally acceptable but **never** store CVV, even hashed, as per PCI DSS!

**Correction:**  
```pseudo
// Do NOT store or process or transmit CVV after initial authorization
// Pseudocode: Remove any CVV storage
// $hashed_cvv = password_hash($cvv, PASSWORD_DEFAULT); // <-- REMOVE
```

---

## 9. **Potential Logic Error in Booking Code Generation Loop**
**Issue:**  
The code uses a loop to regenerate booking codes, but in high concurrency conditions, this may never terminate.

**Correction:**  
```pseudo
// Pseudocode: Add a max attempts safety
$max_attempts = 5; $attempt = 0;
do {
    $booking_code = 'TR' . strtoupper(bin2hex(random_bytes(4)));
    $stmt = $pdo->prepare("SELECT id FROM transport_bookings WHERE booking_code = ?");
    $stmt->execute([$booking_code]);
    $exists = $stmt->fetch();
    $attempt++;
} while ($exists && $attempt < $max_attempts);

if ($exists) {
    // handle code exhaustion error
}
```

---

## 10. **Use of HTTP_USER_AGENT/REMOTE_ADDR Without Validation**
**Issue:**  
`$_SERVER['HTTP_USER_AGENT']` and `$_SERVER['REMOTE_ADDR']` are used directly.  
_User agent is attacker-controlled and can be used for log injection._

**Correction:**  
```pseudo
// Pseudocode: Sanitize HTTP_USER_AGENT before logging/storing
$user_agent = substr(filter_var($_SERVER['HTTP_USER_AGENT'], FILTER_SANITIZE_STRING), 0, 255);
$ip_address = filter_var($_SERVER['REMOTE_ADDR'], FILTER_VALIDATE_IP) ?: '';
```

---

## 11. **Header Injection Risk in Redirects**
**Issue:**  
Using event_id (external input) in header redirects without validation.

**Correction:**  
```pseudo
// Pseudocode: Sanitize IDs in header location
header("Location: checkout.php?event_id=" . urlencode($event_id));
```

---

## 12. **Hardcoded Database Credentials**  
**Issue:**  
Hardcoded DB credentials in function. Should be centralized (config file, env variable, etc.)

**Recommendation:**  
```pseudo
// Pseudocode: Move DB credentials to config or use dependency injection
require_once 'includes/db_config.php'; // and use variables
```

---

## 13. **Error Handling For Database Connection/Queries**
**Issue:**  
Errors are only logged but not exposed/fail gracefully for users.

**Correction:**  
```pseudo
// Pseudocode: On database connection failure, show a generic error to user, not the exception message
catch (Exception $e) {
    error_log("DB error: ".$e->getMessage());
    // Show user-friendly error
}
```

---

## 14. **CSRF Protection Absent**
**Issue:**  
No CSRF tokens or anti-CSRF protections for POST forms.

**Correction:**  
```pseudo
// Pseudocode: Check CSRF token from session vs POSTed token before processing POST actions
if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    // handle CSRF failure
}
```

---

## 15. **Repetitive Pattern Matching for User Agent Parsing**
**Issue:**  
Can be optimized. Not critical but stylistic.

**Correction:**  
```pseudo
// Pseudocode: Consider extracting browser/OS/device detection to a function for reuse.
function detectBrowser($ua) { /*...*/ }
function detectOS($ua) { /*...*/ }
function detectDevice($ua) { /*...*/ }
```

---

## 16. **No Rate Limiting/Anti-Abuse For Payment/Booking**
**Issue:**  
No visible rate limiting to prevent abuse or brute force.

**Recommendation:**  
- Implement server-side rate limiting or usage quotas for payment attempts per user or IP.

---

## 17. **No Input Length Limits**
**Issue:**  
String inputs like passenger_name or phone are not length-limited.

**Correction:**  
```pseudo
// Pseudocode: Enforce max length on string inputs before using/inserting to database
$passenger_name = substr(trim($data['passenger_name']), 0, 100);
$passenger_phone = substr(trim($data['passenger_phone']), 0, 20);
```

---

## 18. **No Output Escaping on Session Storage**
**Issue:**  
When placing strings in `$_SESSION`, consider escaping on output, but attempt to clean or validate before assignment.

**Recommendation:**  
- No change in code, but ensure all session data is sanitized before later displaying to user.

---

## 19. **No Strict Type Declarations**
**Recommendation:**  
- Use PHP 7+ strict types for all methods and functions where possible.

**Correction:**  
```pseudo
// Pseudocode (at top of file or each function declaration)
declare(strict_types=1);
```

---

## 20. **Payment Redirect May Not Pass all Info Reliably**
**Issue:**  
Sensitive data (like `order_id`) passed by GET. Ensure it is not guessable or tied to public information.

**Correction:**  
- Ensure `order_id` is not predictable or based on public info (see PRNG correction above).

---

# **Summary of Major Issues (Prioritize Fixes)**
1. **Session handling (`session_start()`) missing**
2. **PCI DSS violation (no CVV storage)**
3. **Insufficient input validation/sanitization of all user data**
4. **No expiry/CVV validation**
5. **Insecure random generation (ticket/booking)**
6. **Missing CSRF protection**
7. **Hardcoded error reporting in production**
8. **Unoptimized/unsafe user agent and redirect usage**
9. **No rate limiting / abuse checks**

---

# **References**
- [OWASP Secure Coding Practices](https://cheatsheetseries.owasp.org/cheatsheets/Secure_Coding_Practices_Cheat_Sheet.html)
- [PCI DSS Requirements](https://www.pcisecuritystandards.org/)

---

# **Conclusion**
While the code provides solid business logic, it currently falls short of security standards and robust input handling.  
**Implement all high-priority corrections above before deploying to production!**

Feel free to request a full revised version of the code implementing these recommendations.