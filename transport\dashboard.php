<?php
// تضمين ملفات الإعداد وقاعدة البيانات
require_once '../includes/init.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// جلب الإحصائيات من قاعدة البيانات
try {
    // إجمالي الإيرادات
    $db->query("SELECT SUM(total_amount) as total_revenue FROM transport_bookings WHERE payment_status = 'confirmed'");
    $revenue_result = $db->single();
    $total_revenue = $revenue_result['total_revenue'] ?? 0;

    // إجمالي الرحلات
    $db->query("SELECT COUNT(*) as total_trips FROM transport_trips WHERE is_active = 1");
    $trips_result = $db->single();
    $total_trips = $trips_result['total_trips'] ?? 0;

    // عدد السائقين النشطين
    $db->query("SELECT COUNT(*) as active_drivers FROM transport_drivers WHERE is_active = 1 AND status = 'available'");
    $drivers_result = $db->single();
    $active_drivers = $drivers_result['active_drivers'] ?? 0;

    // عدد الحجوزات الجديدة
    $db->query("SELECT COUNT(*) as new_bookings FROM transport_bookings WHERE status = 'pending' OR status = 'confirmed'");
    $bookings_result = $db->single();
    $new_bookings = $bookings_result['new_bookings'] ?? 0;

    // جلب آخر الأنشطة
    $db->query("
        SELECT
            tb.booking_code,
            tb.customer_name,
            tb.created_at,
            tt.departure_time,
            tsp.name as starting_point_name,
            e.title as event_title
        FROM transport_bookings tb
        LEFT JOIN transport_trips tt ON tb.trip_id = tt.id
        LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
        LEFT JOIN events e ON tb.event_id = e.id
        ORDER BY tb.created_at DESC
        LIMIT 5
    ");
    $recent_activities = $db->resultSet();

    // جلب نقاط الانطلاق
    $db->query("SELECT * FROM transport_starting_points WHERE is_active = 1 ORDER BY name ASC");
    $departure_points = $db->resultSet();

    // جلب الرحلات
    $db->query("
        SELECT
            tt.*,
            tsp.name as starting_point_name,
            tty.name as transport_type_name,
            e.title as event_title,
            e.date_time as event_date
        FROM transport_trips tt
        LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
        LEFT JOIN transport_types tty ON tt.transport_type_id = tty.id
        LEFT JOIN events e ON tt.event_id = e.id
        WHERE tt.is_active = 1
        ORDER BY tt.departure_time DESC
        LIMIT 10
    ");
    $trips = $db->resultSet();

    // جلب السائقين
    $db->query("
        SELECT
            td.*,
            tv.plate_number,
            tv.model,
            tv.year,
            tty.name as vehicle_type
        FROM transport_drivers td
        LEFT JOIN transport_vehicles tv ON td.id = tv.driver_id
        LEFT JOIN transport_types tty ON tv.transport_type_id = tty.id
        WHERE td.is_active = 1
        ORDER BY td.name ASC
    ");
    $drivers = $db->resultSet();

    // جلب الحجوزات
    $db->query("
        SELECT
            tb.*,
            tt.departure_time,
            tsp.name as starting_point_name,
            e.title as event_title
        FROM transport_bookings tb
        LEFT JOIN transport_trips tt ON tb.trip_id = tt.id
        LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
        LEFT JOIN events e ON tb.event_id = e.id
        ORDER BY tb.created_at DESC
        LIMIT 20
    ");
    $bookings = $db->resultSet();

} catch (Exception $e) {
    // في حالة حدوث خطأ، استخدم قيم افتراضية
    $total_revenue = 0;
    $total_trips = 0;
    $active_drivers = 0;
    $new_bookings = 0;
    $recent_activities = [];
    $departure_points = [];
    $trips = [];
    $drivers = [];
    $bookings = [];
    error_log("Dashboard Error: " . $e->getMessage());
}

// دالة لتنسيق التاريخ والوقت
function formatDateTime($datetime) {
    if (!$datetime) return 'غير محدد';
    $date = new DateTime($datetime);
    return $date->format('Y-m-d H:i');
}

// دالة لتنسيق المبلغ
function formatPrice($amount) {
    return number_format($amount, 2) . ' ₪';
}

// دالة لتنسيق حالة الحجز
function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">قيد الانتظار</span>',
        'confirmed' => '<span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">مؤكد</span>',
        'cancelled' => '<span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">ملغي</span>',
        'completed' => '<span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">مكتمل</span>'
    ];
    return $badges[$status] ?? '<span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">' . $status . '</span>';
}

// دالة لتنسيق حالة السائق
function getDriverStatusBadge($status) {
    $badges = [
        'available' => '<span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">متاح</span>',
        'busy' => '<span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">مشغول</span>',
        'offline' => '<span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">غير متصل</span>'
    ];
    return $badges[$status] ?? '<span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">' . $status . '</span>';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المواصلات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7e22ce',
                            800: '#6b21a8',
                            900: '#581c87',
                        },
                        secondary: {
                            100: '#f0f9ff',
                            200: '#e0f2fe',
                            300: '#bae6fd',
                            400: '#7dd3fc',
                            500: '#38bdf8',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .dashboard-section {
            display: none;
        }
        .dashboard-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .nav-item.active {
            border-bottom: 3px solid white;
            font-weight: bold;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Main Container -->
    <div class="flex flex-col min-h-screen">
        <!-- Header -->
        <header class="bg-gradient-to-r from-primary-700 to-primary-900 text-white p-4 shadow-lg">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center space-x-2 space-x-reverse">
                    <i class="fas fa-bus text-3xl"></i>
                    <h1 class="text-2xl font-bold">لوحة تحكم المواصلات</h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="relative">
                        <i class="fas fa-bell text-xl cursor-pointer"></i>
                        <span class="absolute -top-1 -right-1 bg-secondary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"><?php echo $new_bookings; ?></span>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="font-medium">المدير</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Horizontal Navigation -->
        <nav class="bg-gradient-to-r from-primary-600 to-primary-800 text-white shadow-md">
            <div class="container mx-auto overflow-x-auto">
                <div class="flex space-x-1 space-x-reverse py-2 px-1">
                    <button onclick="showSection('overview')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition active">
                        <i class="fas fa-home ml-2"></i> نظرة عامة
                    </button>
                    <button onclick="showSection('revenue')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-line ml-2"></i> الإيرادات
                    </button>
                    <button onclick="showSection('departure-points')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-map-marker-alt ml-2"></i> نقاط الانطلاق
                    </button>
                    <button onclick="showSection('trips')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-route ml-2"></i> الرحلات
                    </button>
                    <button onclick="showSection('drivers')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-id-card-alt ml-2"></i> السائقين
                    </button>
                    <button onclick="showSection('bookings')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-calendar-check ml-2"></i> الحجوزات
                    </button>
                    <button onclick="showSection('analytics')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-pie ml-2"></i> التحليلات
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto p-4">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">نظرة عامة على لوحة التحكم</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">إجمالي الإيرادات</p>
                                <h3 class="text-2xl font-bold text-primary-700"><?php echo formatPrice($total_revenue); ?></h3>
                                <p class="text-blue-500 text-sm mt-1"><i class="fas fa-info-circle mr-1"></i> من الحجوزات المؤكدة</p>
                            </div>
                            <div class="bg-primary-100 p-3 rounded-full">
                                <i class="fas fa-shekel-sign text-primary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">إجمالي الرحلات</p>
                                <h3 class="text-2xl font-bold text-secondary-700"><?php echo $total_trips; ?></h3>
                                <p class="text-blue-500 text-sm mt-1"><i class="fas fa-info-circle mr-1"></i> الرحلات النشطة</p>
                            </div>
                            <div class="bg-secondary-100 p-3 rounded-full">
                                <i class="fas fa-route text-secondary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">السائقين المتاحين</p>
                                <h3 class="text-2xl font-bold text-amber-700"><?php echo $active_drivers; ?></h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-check-circle mr-1"></i> جاهزين للعمل</p>
                            </div>
                            <div class="bg-amber-100 p-3 rounded-full">
                                <i class="fas fa-id-card-alt text-amber-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">الحجوزات الجديدة</p>
                                <h3 class="text-2xl font-bold text-emerald-700"><?php echo $new_bookings; ?></h3>
                                <p class="text-blue-500 text-sm mt-1"><i class="fas fa-info-circle mr-1"></i> قيد المعالجة</p>
                            </div>
                            <div class="bg-emerald-100 p-3 rounded-full">
                                <i class="fas fa-calendar-check text-emerald-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">الأنشطة الحديثة</h3>
                        <button class="text-primary-600 hover:text-primary-800">عرض الكل</button>
                    </div>
                    <div class="space-y-4">
                        <?php if (!empty($recent_activities)): ?>
                            <?php foreach ($recent_activities as $activity): ?>
                                <div class="flex items-start space-x-4 space-x-reverse">
                                    <div class="bg-primary-100 p-2 rounded-full">
                                        <i class="fas fa-calendar-check text-primary-600"></i>
                                    </div>
                                    <div class="flex-grow">
                                        <p class="font-medium">حجز جديد #<?php echo htmlspecialchars($activity['booking_code']); ?></p>
                                        <p class="text-gray-500 text-sm">
                                            <?php echo htmlspecialchars($activity['customer_name']); ?>
                                            حجز رحلة إلى <?php echo htmlspecialchars($activity['event_title'] ?? 'فعالية'); ?>
                                            <?php if ($activity['starting_point_name']): ?>
                                                من <?php echo htmlspecialchars($activity['starting_point_name']); ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    <span class="text-gray-400 text-sm">
                                        <?php
                                        $time_diff = time() - strtotime($activity['created_at']);
                                        if ($time_diff < 3600) {
                                            echo floor($time_diff / 60) . ' دقيقة';
                                        } elseif ($time_diff < 86400) {
                                            echo floor($time_diff / 3600) . ' ساعة';
                                        } else {
                                            echo floor($time_diff / 86400) . ' يوم';
                                        }
                                        ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-gray-500 py-8">
                                <i class="fas fa-inbox text-4xl mb-4"></i>
                                <p>لا توجد أنشطة حديثة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">ملخص الإيرادات</h3>
                        <div class="h-64 flex items-center justify-center text-gray-500">
                            <div class="text-center">
                                <i class="fas fa-chart-line text-6xl mb-4"></i>
                                <p>رسم بياني للإيرادات</p>
                                <p class="text-sm">سيتم إضافة الرسوم البيانية قريباً</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">توزيع الرحلات</h3>
                        <div class="h-64 flex items-center justify-center text-gray-500">
                            <div class="text-center">
                                <i class="fas fa-chart-pie text-6xl mb-4"></i>
                                <p>رسم بياني لتوزيع الرحلات</p>
                                <p class="text-sm">سيتم إضافة الرسوم البيانية قريباً</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Revenue Section -->
            <section id="revenue" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">إدارة الإيرادات</h2>
                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">الإيرادات الشهرية</h3>
                        <div class="flex space-x-2 space-x-reverse">
                            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition">تصدير</button>
                            <select class="border rounded-lg px-3 py-2">
                                <option>آخر 6 أشهر</option>
                                <option>هذا العام</option>
                                <option>العام الماضي</option>
                            </select>
                        </div>
                    </div>
                    <div class="h-96 flex items-center justify-center text-gray-500">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-6xl mb-4"></i>
                            <p>الرسم البياني للإيرادات</p>
                            <p class="text-sm">سيتم إضافة الرسوم البيانية قريباً</p>
                        </div>
                    </div>
                </div>

                <?php
                // جلب إحصائيات الإيرادات حسب نوع وسيلة النقل
                try {
                    $db->query("
                        SELECT
                            tt.name as transport_type,
                            COUNT(tb.id) as bookings_count,
                            SUM(tb.total_amount) as total_revenue,
                            AVG(tb.total_amount) as avg_revenue
                        FROM transport_bookings tb
                        LEFT JOIN transport_trips tr ON tb.trip_id = tr.id
                        LEFT JOIN transport_types tt ON tr.transport_type_id = tt.id
                        WHERE tb.payment_status = 'confirmed'
                        GROUP BY tt.id, tt.name
                        ORDER BY total_revenue DESC
                    ");
                    $revenue_by_type = $db->resultSet();
                } catch (Exception $e) {
                    $revenue_by_type = [];
                }
                ?>

                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">الإيرادات حسب نوع وسيلة النقل</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع وسيلة النقل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الحجوزات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي الإيرادات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($revenue_by_type)): ?>
                                    <?php foreach ($revenue_by_type as $revenue): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap font-medium">
                                                <?php echo htmlspecialchars($revenue['transport_type'] ?? 'غير محدد'); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo $revenue['bookings_count']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatPrice($revenue['total_revenue']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatPrice($revenue['avg_revenue']); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                                            <i class="fas fa-chart-bar text-4xl mb-4"></i>
                                            <p>لا توجد بيانات إيرادات</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Departure Points Section -->
            <section id="departure-points" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">نقاط الانطلاق</h2>
                    <button onclick="openAddDepartureModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus ml-2"></i> إضافة جديد
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المعرف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الموقع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المنطقة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($departure_points)): ?>
                                    <?php foreach ($departure_points as $point): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">DP-<?php echo str_pad($point['id'], 3, '0', STR_PAD_LEFT); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap font-medium"><?php echo htmlspecialchars($point['name']); ?></td>
                                            <td class="px-6 py-4"><?php echo htmlspecialchars($point['description'] ?? 'لا يوجد وصف'); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $regions = [
                                                    'north' => 'الشمال',
                                                    'center' => 'الوسط',
                                                    'south' => 'الجنوب'
                                                ];
                                                echo $regions[$point['region']] ?? $point['region'];
                                                ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($point['is_active']): ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">نشط</span>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <button class="text-primary-600 hover:text-primary-800 ml-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                            <i class="fas fa-map-marker-alt text-4xl mb-4"></i>
                                            <p>لا توجد نقاط انطلاق مسجلة</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Trips Section -->
            <section id="trips" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">إدارة الرحلات</h2>
                    <button onclick="openAddTripModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus ml-2"></i> إضافة رحلة جديدة
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">معرف الرحلة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المسار</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">وقت المغادرة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">وقت الوصول</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">السعر</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المقاعد</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($trips)): ?>
                                    <?php foreach ($trips as $trip): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">TR-<?php echo str_pad($trip['id'], 4, '0', STR_PAD_LEFT); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo htmlspecialchars($trip['starting_point_name'] ?? 'نقطة انطلاق'); ?>
                                                إلى
                                                <?php echo htmlspecialchars($trip['event_title'] ?? 'الفعالية'); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatDateTime($trip['departure_time']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo htmlspecialchars($trip['arrival_time']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatPrice($trip['price']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo $trip['available_seats']; ?>/<?php echo $trip['total_seats']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if ($trip['is_active']): ?>
                                                    <?php if ($trip['available_seats'] == 0): ?>
                                                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">مكتمل</span>
                                                    <?php else: ?>
                                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">نشط</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <button class="text-primary-600 hover:text-primary-800 ml-3">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                            <i class="fas fa-route text-4xl mb-4"></i>
                                            <p>لا توجد رحلات مسجلة</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Drivers Section -->
            <section id="drivers" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">إدارة السائقين</h2>
                    <button onclick="openAddDriverModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus ml-2"></i> إضافة سائق جديد
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php if (!empty($drivers)): ?>
                        <?php foreach ($drivers as $driver): ?>
                            <!-- Driver Card -->
                            <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                                <div class="p-6">
                                    <div class="flex items-center space-x-4 space-x-reverse mb-4">
                                        <?php if ($driver['photo']): ?>
                                            <img src="<?php echo htmlspecialchars($driver['photo']); ?>" alt="Driver" class="w-16 h-16 rounded-full object-cover">
                                        <?php else: ?>
                                            <div class="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center">
                                                <i class="fas fa-user text-gray-600 text-2xl"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($driver['name']); ?></h3>
                                            <p class="text-gray-500">معرف السائق: DR-<?php echo str_pad($driver['id'], 4, '0', STR_PAD_LEFT); ?></p>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center">
                                            <i class="fas fa-phone-alt text-primary-600 ml-2"></i>
                                            <span><?php echo htmlspecialchars($driver['phone']); ?></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-id-card text-primary-600 ml-2"></i>
                                            <span>رخصة: <?php echo htmlspecialchars($driver['license_number']); ?></span>
                                        </div>
                                        <?php if ($driver['plate_number']): ?>
                                            <div class="flex items-center">
                                                <i class="fas fa-car text-primary-600 ml-2"></i>
                                                <span>
                                                    <?php echo htmlspecialchars($driver['vehicle_type'] ?? 'مركبة'); ?>:
                                                    <?php echo htmlspecialchars($driver['model'] ?? ''); ?>
                                                    (<?php echo htmlspecialchars($driver['plate_number']); ?>)
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-primary-600 ml-2"></i>
                                            <span>التقييم: <?php echo number_format($driver['rating'], 1); ?>/5</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar text-primary-600 ml-2"></i>
                                            <span>الخبرة: <?php echo $driver['experience_years']; ?> سنة</span>
                                        </div>
                                    </div>
                                    <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                        <?php echo getDriverStatusBadge($driver['status']); ?>
                                        <div>
                                            <button class="text-primary-600 hover:text-primary-800 ml-3">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-span-full text-center text-gray-500 py-12">
                            <i class="fas fa-users text-6xl mb-4"></i>
                            <p class="text-xl">لا يوجد سائقين مسجلين</p>
                            <p class="text-sm mt-2">قم بإضافة سائق جديد للبدء</p>
                        </div>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">إدارة الحجوزات</h2>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">رقم الحجز</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">العميل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الرحلة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التاريخ</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المقاعد</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المبلغ</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($bookings)): ?>
                                    <?php foreach ($bookings as $booking): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap font-medium">
                                                <?php echo htmlspecialchars($booking['booking_code']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo htmlspecialchars($booking['customer_name']); ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($booking['customer_phone']); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <?php echo htmlspecialchars($booking['starting_point_name'] ?? 'نقطة انطلاق'); ?>
                                                إلى
                                                <?php echo htmlspecialchars($booking['event_title'] ?? 'الفعالية'); ?>
                                                <div class="text-sm text-gray-500">
                                                    TR-<?php echo str_pad($booking['trip_id'], 4, '0', STR_PAD_LEFT); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatDateTime($booking['departure_time']); ?>
                                                <div class="text-sm text-gray-500">
                                                    حُجز في: <?php echo date('Y-m-d', strtotime($booking['created_at'])); ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo $booking['passengers_count'] ?? $booking['seats_count']; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo formatPrice($booking['total_amount']); ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php
                                                    $payment_methods = [
                                                        'bank_transfer' => 'تحويل بنكي',
                                                        'cash_on_delivery' => 'دفع عند الاستلام',
                                                        'mobile_pay' => 'دفع إلكتروني',
                                                        'credit_card' => 'بطاقة ائتمان'
                                                    ];
                                                    echo $payment_methods[$booking['payment_method']] ?? $booking['payment_method'];
                                                    ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php echo getStatusBadge($booking['status']); ?>
                                                <div class="text-sm mt-1">
                                                    <?php
                                                    $payment_status_ar = [
                                                        'pending' => 'قيد الانتظار',
                                                        'confirmed' => 'مؤكد',
                                                        'cancelled' => 'ملغي'
                                                    ];
                                                    echo $payment_status_ar[$booking['payment_status']] ?? $booking['payment_status'];
                                                    ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                            <i class="fas fa-calendar-times text-4xl mb-4"></i>
                                            <p>لا توجد حجوزات</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if (!empty($bookings)): ?>
                        <div class="px-6 py-4 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-500">
                                    عرض <span class="font-medium">1</span> إلى <span class="font-medium"><?php echo count($bookings); ?></span> من <span class="font-medium"><?php echo count($bookings); ?></span> حجز
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                        السابق
                                    </button>
                                    <button class="px-3 py-1 border rounded-md text-sm bg-primary-600 text-white hover:bg-primary-700">
                                        1
                                    </button>
                                    <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                        التالي
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">لوحة التحليلات</h2>

                <?php
                // جلب إحصائيات إضافية للتحليلات
                try {
                    // أكثر نقاط الانطلاق استخداماً
                    $db->query("
                        SELECT
                            tsp.name,
                            COUNT(tb.id) as bookings_count
                        FROM transport_bookings tb
                        LEFT JOIN transport_trips tt ON tb.trip_id = tt.id
                        LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
                        WHERE tb.status != 'cancelled'
                        GROUP BY tsp.id, tsp.name
                        ORDER BY bookings_count DESC
                        LIMIT 5
                    ");
                    $popular_points = $db->resultSet();

                    // إحصائيات طرق الدفع
                    $db->query("
                        SELECT
                            payment_method,
                            COUNT(*) as count,
                            SUM(total_amount) as total_amount
                        FROM transport_bookings
                        WHERE payment_status = 'confirmed'
                        GROUP BY payment_method
                        ORDER BY count DESC
                    ");
                    $payment_stats = $db->resultSet();

                } catch (Exception $e) {
                    $popular_points = [];
                    $payment_stats = [];
                }
                ?>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">أكثر نقاط الانطلاق استخداماً</h3>
                        <div class="space-y-4">
                            <?php if (!empty($popular_points)): ?>
                                <?php foreach ($popular_points as $point): ?>
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium"><?php echo htmlspecialchars($point['name']); ?></span>
                                        <span class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                                            <?php echo $point['bookings_count']; ?> حجز
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-gray-500 text-center py-8">لا توجد بيانات متاحة</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">إحصائيات طرق الدفع</h3>
                        <div class="space-y-4">
                            <?php if (!empty($payment_stats)): ?>
                                <?php foreach ($payment_stats as $payment): ?>
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium">
                                            <?php
                                            $payment_methods = [
                                                'bank_transfer' => 'تحويل بنكي',
                                                'cash_on_delivery' => 'دفع عند الاستلام',
                                                'mobile_pay' => 'دفع إلكتروني',
                                                'credit_card' => 'بطاقة ائتمان'
                                            ];
                                            echo $payment_methods[$payment['payment_method']] ?? $payment['payment_method'];
                                            ?>
                                        </span>
                                        <div class="text-left">
                                            <div class="font-medium"><?php echo $payment['count']; ?> معاملة</div>
                                            <div class="text-sm text-gray-500"><?php echo formatPrice($payment['total_amount']); ?></div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-gray-500 text-center py-8">لا توجد بيانات متاحة</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">ملخص الأداء</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-primary-600"><?php echo $total_trips; ?></div>
                            <div class="text-gray-500">إجمالي الرحلات</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-secondary-600"><?php echo $new_bookings; ?></div>
                            <div class="text-gray-500">الحجوزات النشطة</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-emerald-600"><?php echo formatPrice($total_revenue); ?></div>
                            <div class="text-gray-500">إجمالي الإيرادات</div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-100 border-t border-gray-200 py-4">
            <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
                <p>© 2024 لوحة تحكم المواصلات. جميع الحقوق محفوظة.</p>
            </div>
        </footer>
    </div>

    <!-- Add Departure Point Modal -->
    <div id="addDepartureModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Departure Point</h3>
                    <button onclick="closeAddDepartureModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Location Name</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" rows="2"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Active</option>
                                <option>Maintenance</option>
                                <option>Closed</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDepartureModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Trip Modal -->
    <div id="addTripModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Trip</h3>
                    <button onclick="closeAddTripModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Route</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>New York to Boston</option>
                                <option>Los Angeles to San Francisco</option>
                                <option>Chicago to Detroit</option>
                                <option>Miami to Orlando</option>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Date</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Time</label>
                                <input type="time" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Seats</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>John Smith</option>
                                <option>Sarah Johnson</option>
                                <option>Michael Brown</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddTripModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Driver Modal -->
    <div id="addDriverModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Driver</h3>
                    <button onclick="closeAddDriverModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver License</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Assigned Vehicle</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Ford Transit (ABC123)</option>
                                <option>Mercedes Sprinter (XYZ789)</option>
                                <option>Chevrolet Express (DEF456)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDriverModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Find the button that was clicked and make it active
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(button => {
                if (button.getAttribute('onclick') === `showSection('${sectionId}')`) {
                    button.classList.add('active');
                }
            });
        }

        // Modal functions
        function openAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.remove('hidden');
        }

        function closeAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.add('hidden');
        }

        function openAddTripModal() {
            document.getElementById('addTripModal').classList.remove('hidden');
        }

        function closeAddTripModal() {
            document.getElementById('addTripModal').classList.add('hidden');
        }

        function openAddDriverModal() {
            document.getElementById('addDriverModal').classList.remove('hidden');
        }

        function closeAddDriverModal() {
            document.getElementById('addDriverModal').classList.add('hidden');
        }

        // تحديث الوقت كل دقيقة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            // يمكن إضافة عنصر لعرض الوقت إذا أردت
            console.log('الوقت الحالي:', timeString);
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);
    </script>
</body>
</html>