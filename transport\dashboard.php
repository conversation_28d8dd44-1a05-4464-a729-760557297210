<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            100: '#f3e8ff',
                            200: '#e9d5ff',
                            300: '#d8b4fe',
                            400: '#c084fc',
                            500: '#a855f7',
                            600: '#9333ea',
                            700: '#7e22ce',
                            800: '#6b21a8',
                            900: '#581c87',
                        },
                        secondary: {
                            100: '#f0f9ff',
                            200: '#e0f2fe',
                            300: '#bae6fd',
                            400: '#7dd3fc',
                            500: '#38bdf8',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .dashboard-section {
            display: none;
        }
        .dashboard-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .nav-item.active {
            border-bottom: 3px solid white;
            font-weight: bold;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Main Container -->
    <div class="flex flex-col min-h-screen">
        <!-- Header -->
        <header class="bg-gradient-to-r from-primary-700 to-primary-900 text-white p-4 shadow-lg">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-plane-departure text-3xl"></i>
                    <h1 class="text-2xl font-bold">TravelPro Dashboard</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <i class="fas fa-bell text-xl cursor-pointer"></i>
                        <span class="absolute -top-1 -right-1 bg-secondary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="font-medium">Admin</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Horizontal Navigation -->
        <nav class="bg-gradient-to-r from-primary-600 to-primary-800 text-white shadow-md">
            <div class="container mx-auto overflow-x-auto">
                <div class="flex space-x-1 py-2 px-1">
                    <button onclick="showSection('overview')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition active">
                        <i class="fas fa-home mr-2"></i> Overview
                    </button>
                    <button onclick="showSection('revenue')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-line mr-2"></i> Revenue
                    </button>
                    <button onclick="showSection('departure-points')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-map-marker-alt mr-2"></i> Departure Points
                    </button>
                    <button onclick="showSection('trips')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-route mr-2"></i> Trips
                    </button>
                    <button onclick="showSection('drivers')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-id-card-alt mr-2"></i> Drivers
                    </button>
                    <button onclick="showSection('bookings')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-calendar-check mr-2"></i> Bookings
                    </button>
                    <button onclick="showSection('analytics')" class="nav-item px-4 py-3 rounded-lg hover:bg-primary-500 transition">
                        <i class="fas fa-chart-pie mr-2"></i> Analytics
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto p-4">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Dashboard Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Total Revenue</p>
                                <h3 class="text-2xl font-bold text-primary-700">$24,780</h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-arrow-up mr-1"></i> 12% from last month</p>
                            </div>
                            <div class="bg-primary-100 p-3 rounded-full">
                                <i class="fas fa-dollar-sign text-primary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Total Trips</p>
                                <h3 class="text-2xl font-bold text-secondary-700">187</h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-arrow-up mr-1"></i> 8% from last month</p>
                            </div>
                            <div class="bg-secondary-100 p-3 rounded-full">
                                <i class="fas fa-route text-secondary-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">Active Drivers</p>
                                <h3 class="text-2xl font-bold text-amber-700">24</h3>
                                <p class="text-red-500 text-sm mt-1"><i class="fas fa-arrow-down mr-1"></i> 2% from last month</p>
                            </div>
                            <div class="bg-amber-100 p-3 rounded-full">
                                <i class="fas fa-id-card-alt text-amber-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 card-hover transition">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-500">New Bookings</p>
                                <h3 class="text-2xl font-bold text-emerald-700">56</h3>
                                <p class="text-green-500 text-sm mt-1"><i class="fas fa-arrow-up mr-1"></i> 15% from last month</p>
                            </div>
                            <div class="bg-emerald-100 p-3 rounded-full">
                                <i class="fas fa-calendar-check text-emerald-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">Recent Activities</h3>
                        <button class="text-primary-600 hover:text-primary-800">View All</button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-4">
                            <div class="bg-primary-100 p-2 rounded-full">
                                <i class="fas fa-calendar-check text-primary-600"></i>
                            </div>
                            <div class="flex-grow">
                                <p class="font-medium">New booking #B-78945</p>
                                <p class="text-gray-500 text-sm">John Doe booked a trip to Paris</p>
                            </div>
                            <span class="text-gray-400 text-sm">2 min ago</span>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="bg-secondary-100 p-2 rounded-full">
                                <i class="fas fa-user-plus text-secondary-600"></i>
                            </div>
                            <div class="flex-grow">
                                <p class="font-medium">New driver added</p>
                                <p class="text-gray-500 text-sm">Michael Brown joined as a new driver</p>
                            </div>
                            <span class="text-gray-400 text-sm">1 hour ago</span>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="bg-amber-100 p-2 rounded-full">
                                <i class="fas fa-route text-amber-600"></i>
                            </div>
                            <div class="flex-grow">
                                <p class="font-medium">New trip created</p>
                                <p class="text-gray-500 text-sm">New York to Miami route added</p>
                            </div>
                            <span class="text-gray-400 text-sm">3 hours ago</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue Overview</h3>
                        <div class="h-64">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Trip Distribution</h3>
                        <div class="h-64">
                            <canvas id="tripChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Revenue Section -->
            <section id="revenue" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Revenue Management</h2>
                <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">Monthly Revenue</h3>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition">Export</button>
                            <select class="border rounded-lg px-3 py-2">
                                <option>Last 6 Months</option>
                                <option>This Year</option>
                                <option>Last Year</option>
                            </select>
                        </div>
                    </div>
                    <div class="h-96">
                        <canvas id="detailedRevenueChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue by Trip Type</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Revenue</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">Standard</td>
                                    <td class="px-6 py-4 whitespace-nowrap">124</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$12,450</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$100.40</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">Premium</td>
                                    <td class="px-6 py-4 whitespace-nowrap">56</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$8,750</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$156.25</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">Luxury</td>
                                    <td class="px-6 py-4 whitespace-nowrap">7</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$3,580</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$511.43</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Departure Points Section -->
            <section id="departure-points" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Departure Points</h2>
                    <button onclick="openAddDepartureModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Location</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">DP-001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">New York Central</td>
                                    <td class="px-6 py-4">123 Broadway, New York, NY 10001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">(212) 555-1234</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">DP-002</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Los Angeles Terminal</td>
                                    <td class="px-6 py-4">456 Sunset Blvd, Los Angeles, CA 90028</td>
                                    <td class="px-6 py-4 whitespace-nowrap">(213) 555-5678</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">DP-003</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Chicago Hub</td>
                                    <td class="px-6 py-4">789 Michigan Ave, Chicago, IL 60611</td>
                                    <td class="px-6 py-4 whitespace-nowrap">(312) 555-9012</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Maintenance</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">DP-004</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Miami Beach Station</td>
                                    <td class="px-6 py-4">101 Ocean Dr, Miami, FL 33139</td>
                                    <td class="px-6 py-4 whitespace-nowrap">(305) 555-3456</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Trips Section -->
            <section id="trips" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Trip Management</h2>
                    <button onclick="openAddTripModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New Trip
                    </button>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Trip ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Route</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Departure</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Arrival</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Seats</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">TR-1001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">New York to Boston</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 15, 08:00 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 15, 12:30 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$89.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">24/30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">TR-1002</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Los Angeles to San Francisco</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 16, 09:00 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 16, 04:30 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$129.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">18/30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">TR-1003</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Chicago to Detroit</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 17, 07:30 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 17, 12:00 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$75.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">30/30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Full</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">TR-1004</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Miami to Orlando</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 18, 10:00 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 18, 01:30 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$59.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">15/30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <button class="text-primary-600 hover:text-primary-800 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Drivers Section -->
            <section id="drivers" class="dashboard-section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-primary-900">Driver Management</h2>
                    <button onclick="openAddDriverModal()" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition flex items-center">
                        <i class="fas fa-plus mr-2"></i> Add New Driver
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Driver Card 1 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="p-6">
                            <div class="flex items-center space-x-4 mb-4">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Driver" class="w-16 h-16 rounded-full">
                                <div>
                                    <h3 class="text-lg font-semibold">John Smith</h3>
                                    <p class="text-gray-500">Driver ID: DR-1001</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-phone-alt text-primary-600 mr-2"></i>
                                    <span>(*************</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-primary-600 mr-2"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-id-card text-primary-600 mr-2"></i>
                                    <span>License: NY123456789</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-car text-primary-600 mr-2"></i>
                                    <span>Vehicle: Ford Transit (ABC123)</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                                <div>
                                    <button class="text-primary-600 hover:text-primary-800 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Driver Card 2 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="p-6">
                            <div class="flex items-center space-x-4 mb-4">
                                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Driver" class="w-16 h-16 rounded-full">
                                <div>
                                    <h3 class="text-lg font-semibold">Sarah Johnson</h3>
                                    <p class="text-gray-500">Driver ID: DR-1002</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-phone-alt text-primary-600 mr-2"></i>
                                    <span>(*************</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-primary-600 mr-2"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-id-card text-primary-600 mr-2"></i>
                                    <span>License: CA987654321</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-car text-primary-600 mr-2"></i>
                                    <span>Vehicle: Mercedes Sprinter (XYZ789)</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                                <div>
                                    <button class="text-primary-600 hover:text-primary-800 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Driver Card 3 -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="p-6">
                            <div class="flex items-center space-x-4 mb-4">
                                <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Driver" class="w-16 h-16 rounded-full">
                                <div>
                                    <h3 class="text-lg font-semibold">Michael Brown</h3>
                                    <p class="text-gray-500">Driver ID: DR-1003</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-phone-alt text-primary-600 mr-2"></i>
                                    <span>(*************</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-primary-600 mr-2"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-id-card text-primary-600 mr-2"></i>
                                    <span>License: IL456123789</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-car text-primary-600 mr-2"></i>
                                    <span>Vehicle: Chevrolet Express (DEF456)</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">On Leave</span>
                                <div>
                                    <button class="text-primary-600 hover:text-primary-800 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Booking Management</h2>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-primary-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Booking ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Trip</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Seats</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">B-78945</td>
                                    <td class="px-6 py-4 whitespace-nowrap">John Doe</td>
                                    <td class="px-6 py-4">New York to Boston (TR-1001)</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 15, 2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$178.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">B-78946</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jane Smith</td>
                                    <td class="px-6 py-4">Los Angeles to San Francisco (TR-1002)</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 16, 2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$129.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">B-78947</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Robert Johnson</td>
                                    <td class="px-6 py-4">Chicago to Detroit (TR-1003)</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 17, 2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap">4</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$300.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Pending</span>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">B-78948</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Emily Davis</td>
                                    <td class="px-6 py-4">Miami to Orlando (TR-1004)</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 18, 2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$177.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Confirmed</span>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">B-78949</td>
                                    <td class="px-6 py-4 whitespace-nowrap">David Wilson</td>
                                    <td class="px-6 py-4">New York to Boston (TR-1001)</td>
                                    <td class="px-6 py-4 whitespace-nowrap">Jun 15, 2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap">$89.00</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Cancelled</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">24</span> bookings
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    Previous
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-primary-600 text-white hover:bg-primary-700">
                                    1
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    2
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    3
                                </button>
                                <button class="px-3 py-1 border rounded-md text-sm bg-white text-gray-700 hover:bg-gray-50">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="dashboard-section">
                <h2 class="text-2xl font-bold text-primary-900 mb-6">Analytics Dashboard</h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Booking Trends</h3>
                        <div class="h-80">
                            <canvas id="bookingTrendsChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Revenue by Route</h3>
                        <div class="h-80">
                            <canvas id="revenueByRouteChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Customer Demographics</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-medium text-gray-700 mb-3">Age Distribution</h4>
                            <div class="h-64">
                                <canvas id="ageDistributionChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-700 mb-3">Gender Distribution</h4>
                            <div class="h-64">
                                <canvas id="genderDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-100 border-t border-gray-200 py-4">
            <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
                <p>© 2023 TravelPro Dashboard. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Add Departure Point Modal -->
    <div id="addDepartureModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Departure Point</h3>
                    <button onclick="closeAddDepartureModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Location Name</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500" rows="2"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Active</option>
                                <option>Maintenance</option>
                                <option>Closed</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDepartureModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Trip Modal -->
    <div id="addTripModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Trip</h3>
                    <button onclick="closeAddTripModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Route</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>New York to Boston</option>
                                <option>Los Angeles to San Francisco</option>
                                <option>Chicago to Detroit</option>
                                <option>Miami to Orlando</option>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Date</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Time</label>
                                <input type="time" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Seats</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>John Smith</option>
                                <option>Sarah Johnson</option>
                                <option>Michael Brown</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddTripModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Driver Modal -->
    <div id="addDriverModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-md">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Add New Driver</h3>
                    <button onclick="closeAddDriverModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Driver License</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Assigned Vehicle</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Ford Transit (ABC123)</option>
                                <option>Mercedes Sprinter (XYZ789)</option>
                                <option>Chevrolet Express (DEF456)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddDriverModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Find the button that was clicked and make it active
            const buttons = document.querySelectorAll('.nav-item');
            buttons.forEach(button => {
                if (button.getAttribute('onclick') === `showSection('${sectionId}')`) {
                    button.classList.add('active');
                }
            });
            
            // Initialize charts when section is shown
            if (sectionId === 'overview') {
                initOverviewCharts();
            } else if (sectionId === 'revenue') {
                initRevenueCharts();
            } else if (sectionId === 'analytics') {
                initAnalyticsCharts();
            }
        }

        // Modal functions
        function openAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.remove('hidden');
        }

        function closeAddDepartureModal() {
            document.getElementById('addDepartureModal').classList.add('hidden');
        }

        function openAddTripModal() {
            document.getElementById('addTripModal').classList.remove('hidden');
        }

        function closeAddTripModal() {
            document.getElementById('addTripModal').classList.add('hidden');
        }

        function openAddDriverModal() {
            document.getElementById('addDriverModal').classList.remove('hidden');
        }

        function closeAddDriverModal() {
            document.getElementById('addDriverModal').classList.add('hidden');
        }

        // Chart initialization functions
        function initOverviewCharts() {
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Revenue',
                        data: [12000, 19000, 15000, 20000, 22000, 24780],
                        backgroundColor: 'rgba(168, 85, 247, 0.1)',
                        borderColor: 'rgba(168, 85, 247, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Trip Chart
            const tripCtx = document.getElementById('tripChart').getContext('2d');
            new Chart(tripCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Standard', 'Premium', 'Luxury'],
                    datasets: [{
                        data: [124, 56, 7],
                        backgroundColor: [
                            'rgba(168, 85, 247, 0.7)',
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(245, 158, 11, 0.7)'
                        ],
                        borderColor: [
                            'rgba(168, 85, 247, 1)',
                            'rgba(56, 189, 248, 1)',
                            'rgba(245, 158, 11, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initRevenueCharts() {
            // Detailed Revenue Chart
            const detailedRevenueCtx = document.getElementById('detailedRevenueChart').getContext('2d');
            new Chart(detailedRevenueCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                        {
                            label: 'Standard',
                            data: [8000, 12000, 9000, 11000, 13000, 12450],
                            backgroundColor: 'rgba(168, 85, 247, 0.7)'
                        },
                        {
                            label: 'Premium',
                            data: [3000, 5000, 4000, 7000, 6000, 8750],
                            backgroundColor: 'rgba(56, 189, 248, 0.7)'
                        },
                        {
                            label: 'Luxury',
                            data: [1000, 2000, 2000, 2000, 3000, 3580],
                            backgroundColor: 'rgba(245, 158, 11, 0.7)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            stacked: false,
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function initAnalyticsCharts() {
            // Booking Trends Chart
            const bookingTrendsCtx = document.getElementById('bookingTrendsChart').getContext('2d');
            new Chart(bookingTrendsCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [
                        {
                            label: 'Bookings',
                            data: [45, 78, 62, 89, 102, 120],
                            borderColor: 'rgba(168, 85, 247, 1)',
                            backgroundColor: 'rgba(168, 85, 247, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Cancellations',
                            data: [5, 8, 12, 6, 9, 7],
                            borderColor: 'rgba(239, 68, 68, 1)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Revenue by Route Chart
            const revenueByRouteCtx = document.getElementById('revenueByRouteChart').getContext('2d');
            new Chart(revenueByRouteCtx, {
                type: 'bar',
                data: {
                    labels: ['NY-Boston', 'LA-SF', 'Chicago-Detroit', 'Miami-Orlando'],
                    datasets: [{
                        label: 'Revenue',
                        data: [12450, 8750, 3000, 1770],
                        backgroundColor: [
                            'rgba(168, 85, 247, 0.7)',
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(245, 158, 11, 0.7)',
                            'rgba(16, 185, 129, 0.7)'
                        ],
                        borderColor: [
                            'rgba(168, 85, 247, 1)',
                            'rgba(56, 189, 248, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Age Distribution Chart
            const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
            new Chart(ageDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['18-24', '25-34', '35-44', '45-54', '55+'],
                    datasets: [{
                        label: 'Customers',
                        data: [15, 45, 30, 25, 10],
                        backgroundColor: 'rgba(168, 85, 247, 0.7)',
                        borderColor: 'rgba(168, 85, 247, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gender Distribution Chart
            const genderDistributionCtx = document.getElementById('genderDistributionChart').getContext('2d');
            new Chart(genderDistributionCtx, {
                type: 'pie',
                data: {
                    labels: ['Male', 'Female', 'Other'],
                    datasets: [{
                        data: [55, 40, 5],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(244, 114, 182, 0.7)',
                            'rgba(16, 185, 129, 0.7)'
                        ],
                        borderColor: [
                            'rgba(56, 189, 248, 1)',
                            'rgba(244, 114, 182, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Initialize overview charts on page load
        document.addEventListener('DOMContentLoaded', function() {
            initOverviewCharts();
        });
    </script>
</body>
</html>