<?php
require_once '../../includes/init.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_POST['action'] ?? '';
$db = new Database();

try {
    switch ($action) {
        case 'delete':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // التحقق من وجود مركبات مرتبطة بهذا السائق
            $db->query("SELECT COUNT(*) as count FROM transport_vehicles WHERE driver_id = :id");
            $db->bind(':id', $id);
            $result = $db->single();
            
            if ($result['count'] > 0) {
                // تحديث المركبات لإزالة ربطها بالسائق
                $db->query("UPDATE transport_vehicles SET driver_id = NULL WHERE driver_id = :id");
                $db->bind(':id', $id);
                $db->execute();
            }

            // حذف السائق
            $db->query("DELETE FROM transport_drivers WHERE id = :id");
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم حذف السائق بنجاح']);
            } else {
                throw new Exception('فشل في حذف السائق');
            }
            break;

        case 'update':
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $license_number = trim($_POST['license_number'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $status = trim($_POST['status'] ?? 'available');
            $experience_years = (int)($_POST['experience_years'] ?? 0);
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }
            if (empty($name)) {
                throw new Exception('اسم السائق مطلوب');
            }
            if (empty($phone)) {
                throw new Exception('رقم الهاتف مطلوب');
            }
            if (empty($license_number)) {
                throw new Exception('رقم الرخصة مطلوب');
            }

            // التحقق من عدم تكرار رقم الرخصة
            $db->query("SELECT id FROM transport_drivers WHERE license_number = :license_number AND id != :id");
            $db->bind(':license_number', $license_number);
            $db->bind(':id', $id);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('رقم الرخصة موجود بالفعل');
            }

            // تحديث السائق
            $db->query("
                UPDATE transport_drivers 
                SET name = :name, phone = :phone, license_number = :license_number, 
                    address = :address, status = :status, experience_years = :experience_years,
                    is_active = :is_active, updated_at = NOW()
                WHERE id = :id
            ");
            $db->bind(':name', $name);
            $db->bind(':phone', $phone);
            $db->bind(':license_number', $license_number);
            $db->bind(':address', $address);
            $db->bind(':status', $status);
            $db->bind(':experience_years', $experience_years);
            $db->bind(':is_active', $is_active);
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث بيانات السائق بنجاح']);
            } else {
                throw new Exception('فشل في تحديث بيانات السائق');
            }
            break;

        case 'add':
            $name = trim($_POST['name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $license_number = trim($_POST['license_number'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $status = trim($_POST['status'] ?? 'available');
            $experience_years = (int)($_POST['experience_years'] ?? 0);
            $rating = 5.00; // تقييم افتراضي للسائق الجديد
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if (empty($name)) {
                throw new Exception('اسم السائق مطلوب');
            }
            if (empty($phone)) {
                throw new Exception('رقم الهاتف مطلوب');
            }
            if (empty($license_number)) {
                throw new Exception('رقم الرخصة مطلوب');
            }

            // التحقق من عدم تكرار رقم الرخصة
            $db->query("SELECT id FROM transport_drivers WHERE license_number = :license_number");
            $db->bind(':license_number', $license_number);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('رقم الرخصة موجود بالفعل');
            }

            // إضافة سائق جديد
            $db->query("
                INSERT INTO transport_drivers (name, phone, license_number, address, status, 
                                             experience_years, rating, is_active, created_at, updated_at)
                VALUES (:name, :phone, :license_number, :address, :status, 
                        :experience_years, :rating, :is_active, NOW(), NOW())
            ");
            $db->bind(':name', $name);
            $db->bind(':phone', $phone);
            $db->bind(':license_number', $license_number);
            $db->bind(':address', $address);
            $db->bind(':status', $status);
            $db->bind(':experience_years', $experience_years);
            $db->bind(':rating', $rating);
            $db->bind(':is_active', $is_active);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة السائق بنجاح']);
            } else {
                throw new Exception('فشل في إضافة السائق');
            }
            break;

        case 'get':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            $db->query("
                SELECT 
                    td.*,
                    tv.plate_number,
                    tv.model,
                    tv.year,
                    tty.name as vehicle_type
                FROM transport_drivers td
                LEFT JOIN transport_vehicles tv ON td.id = tv.driver_id
                LEFT JOIN transport_types tty ON tv.transport_type_id = tty.id
                WHERE td.id = :id
            ");
            $db->bind(':id', $id);
            $driver = $db->single();
            
            if (!$driver) {
                throw new Exception('السائق غير موجود');
            }

            echo json_encode(['success' => true, 'data' => $driver]);
            break;

        case 'toggle_status':
            $id = (int)($_POST['id'] ?? 0);
            $new_status = trim($_POST['new_status'] ?? '');
            
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }
            
            $valid_statuses = ['available', 'busy', 'offline'];
            if (!in_array($new_status, $valid_statuses)) {
                throw new Exception('حالة غير صحيحة');
            }

            $db->query("UPDATE transport_drivers SET status = :status, updated_at = NOW() WHERE id = :id");
            $db->bind(':status', $new_status);
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                $status_names = [
                    'available' => 'متاح',
                    'busy' => 'مشغول',
                    'offline' => 'غير متصل'
                ];
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة السائق إلى: ' . $status_names[$new_status]]);
            } else {
                throw new Exception('فشل في تغيير حالة السائق');
            }
            break;

        default:
            throw new Exception('عملية غير مدعومة');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
