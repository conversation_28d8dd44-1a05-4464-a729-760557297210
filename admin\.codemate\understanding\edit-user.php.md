# High-Level Documentation – Edit User Admin Page

## Overview

This PHP script is an admin panel page for editing an existing user in a web application. It ensures only admin users can access and modify user information and allows secure modification of essential fields. Additional user history, specifically their ticket purchases, is also displayed for administrative reference.

---

## Key Features

### 1. Access Control & Validation

- **Admin Only:** Access is limited to admin users; non-admins are redirected.
- **User Validation:** The script checks for a valid `id` parameter and ensures the specified user exists. If not, it redirects to the users list.

### 2. Editing User Information

- **Fields Editable:** Name, phone number, and user role (“user” or “admin”) can be modified. The email address is shown but cannot be changed.
- **Password Management:** Password is optional to update; if provided, it must be at least 6 characters and is securely hashed before storage.

### 3. Form Security

- **CSRF Protection:** Includes a CSRF token for form submission, which is validated on POST.
- **Input Sanitization:** User input fields are sanitized before processing.
- **Error & Success Feedback:** Validation errors and update results are shown using session messages.

### 4. Ticket History Display

- The page shows a table of the selected user's purchased tickets, including event details, ticket quantity, total price, purchase date, and payment status.

### 5. Multilingual Support

- All labels, messages, and placeholders use language array variables (e.g., `$lang['name']`) for multi-language support.

---

## Structure

1. **Header & Auth**
   - Loads global configs, UI header, and authentication helpers.
   - Ensures the user is an admin.

2. **User Validation**
   - Fetches user details by ID.
   - Redirects with an error if the user does not exist.

3. **Form Handling**
   - Processes POST requests to update user info.
   - Validates CSRF token, required fields, and password rules.
   - Updates user in the database; optionally resets password if provided.

4. **Page Rendering**
   - Displays a form pre-filled with user data.
   - Shows a table listing the user's ticket purchase history.

5. **Footer**
   - Loads the admin panel footer.

---

## Additional Notes

- **Design Framework:** Uses Bootstrap for styling.
- **Helper Functions:** Relies on utility functions for CSRF handling, input sanitation, date/price formatting, and password hashing.
- **Redirection:** Handles both success (after update) and error scenarios with appropriate redirection and session messaging.

---

**Summary:**  
This script provides a secure and user-friendly admin interface for updating user profiles and reviewing their ticket purchase history, with all necessary validation, authorization, and security best practices implemented.