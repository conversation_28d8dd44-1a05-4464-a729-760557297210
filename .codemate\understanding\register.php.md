# High-Level Documentation: User Registration Page

## Overview

This PHP file implements a **user registration page** with the following primary functions:
- Displaying a registration form to users who are not logged in.
- Validating and processing registration form submissions.
- Creating a new user account in the system.
- Handling success and error feedback, both in Arabic and English.
- Sending a welcome notification to the newly registered user.
- Supporting RTL/LTR languages by adapting the form UI.

---

## Main Components

### 1. **Dependency Inclusions**
Imports several required PHP files for initialization, functions, authentication, notifications, and header/footer UI.

---

### 2. **Authentication Check**
- If a user is **already logged in**, redirect them to the home page (`index.php`).
- If not logged in, proceed with registration logic.

---

### 3. **Form Handling**
- On **POST request** (form submission):  
    - Retrieve the name, email, phone, password, and password confirmation from the POST data.
    - Check that `password` and `confirm_password` match.
        - If not, display an error (in Arabic or English depending on `$lang`).
    - If passwords match, attempt to register the user using the authentication class.
        - On successful registration:
            - Mark registration as successful.
            - Retrieve the new user’s ID from the database.
            - Send a welcome notification to the new user.
        - If registration fails, display a generic error message.

---

### 4. **User Interface (HTML + Tailwind CSS)**
- Displays a styled registration form (full name, email, phone, password, and password confirmation fields).
- Fields adapt layout based on selected language's direction (RTL or LTR).
- Shows dynamic error/success messages.
- Provides a link to the login page for returning users.

---

### 5. **Multilingual Support**
- Uses `$lang` array for labels, placeholders, and messages to support both Arabic and English.
- Adjusts field icon placement and input paddings for both LTR and RTL languages.

---

### 6. **Notifications**
- Uses a custom function to send a personalized "account created" notification to the new user after a successful registration.

---

### 7. **Footer Inclusion**
- Standard website footer is loaded at the end of the document.

---

## Summary

This code provides a secure, user-friendly, and multilingual user registration workflow for a website, ensuring only non-logged-in users can register, validating form input, creating accounts, giving feedback, and sending welcome notifications. It maintains a responsive design and is easily extendable for further localization or UI improvements.