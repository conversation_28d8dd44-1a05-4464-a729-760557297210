# Critical Code Review Report

**File:** `tickets.php`  
**Purpose:** Admin manage tickets view/page.

---

## 1. Security

### **a) CSRF Token Leakage**
- The CSRF token is generated once per page and re-used on each per-ticket modal. If more than one admin has the same page open, the same token is used multiple times.

**Suggested code:**
```php
// Instead of a global CSRF token, generate a per-form CSRF token
$csrf_token = generateCSRFToken(); // For each ticket inside the foreach
```
Put **inside** the `foreach ($tickets as $ticket):` block:
```php
$csrf_token = generateCSRFToken();
```
Then use `<?php echo $csrf_token; ?>` **inside that particular form**.

---

### **b) Lack of Prepared Statements in SELECT Query**
- The tickets query does not use parameters, but it’s best practice to use prepared statements everywhere to prevent future SQL injection if query changes.

---

### **c) XSS – Unescaped Output**
- Most output, e.g. user and event data, is `htmlspecialchars()`, which is correct.
- No obvious unescaped user inputs identified here.
- Double check `formatPrice()` and `formatDate()` functions do not output unsafe input.

---

### **d) No Output Escaping for `$error_message` in error session**
- When catching a DB error, the exception message is written directly to session without sanitizing.

**Suggested code:**
```php
$_SESSION['error_message'] = 'Database error occurred: ' . htmlspecialchars($e->getMessage());
```

---

## 2. Optimization and Maintainability

### **a) Repeated Modal HTML per Ticket**
- Every ticket in the table outputs a unique modal (one per row), which can be a performance issue if there are hundreds/thousands of tickets.

**Suggested code (pseudo):**
```pseudo
// Instead, use a single hidden modal.
// Populate modal data (ticket id, status, etc) via JS when 'Edit' is clicked.
// Update modal form values via JS each time before showing.

Only output:

<div class="modal fade" id="statusModal" ...>
    ...
    <form id="statusForm"> <!-- No ID suffix -->
        ...
        <input type="hidden" name="ticket_id" id="modal-ticket-id">
        <select ... id="modal-status" ...>
```
JavaScript:
```javascript
document.querySelectorAll('.save-status').forEach(btn => {
    btn.addEventListener('click', function(event) {
        // Set modal inputs based on clicked row/ticket
        // Show modal
    });
});
```

---

### **b) Hardcoded Table Column Indices in JS**
- `row.querySelector('td:nth-child(7)')` is brittle if column order changes.
- Use data attribute for column cell or class for the status cell.

**Suggested code:**
```php
<td class="status-cell">
    <!-- status badge here -->
</td>
```
JS:
```javascript
const statusCell = row.querySelector('.status-cell');
```

---

## 3. Usability

### **a) Modal Closes Immediately After Success**
- Closing the modal after 1s may not be enough for accessibility/reading time.
- Consider requiring user to close modal or longer timeout.

### **b) No Failure Handling in Closing Modal**
- If AJAX fails or nothing changes, the modal still closes.

---

## 4. General Coding Best Practices

### **a) No HTML5 Form Submission/Validation**
- The modal save button is `type="button"`, and has no HTML5 validation. If required, add proper validation or at least `type="submit"` for possible later handling.

**Suggested code:**
```html
<!-- Add 'required' to status select field -->
<select class="form-select" ... required>
```

---

### **b) Localization Fallback**
- The code using the fallback for `$lang['ticket_updated'] ?? "Status updated successfully!"` is correct.

---

### **c) No Try/Catch on Fetch**
- The fetch JS does not handle network errors robustly (but this is minor).

---

## 5. Recommended Code Snippets

### 1. Per-form CSRF tokens
```php
// Inside foreach
$csrf_token = generateCSRFToken();
// Use $csrf_token in each form for the ticket
```

### 2. Error message escaping
```php
$_SESSION['error_message'] = 'Database error occurred: ' . htmlspecialchars($e->getMessage());
```

### 3. Use class for status cell (PHP)
```php
<td class="status-cell">
    <?php // badge and status ?>
</td>
```

### 4. Use JS to select by class (JavaScript)
```javascript
const statusCell = row.querySelector('.status-cell');
```

### 5. Use single modal for all status updates (pseudo)
```pseudo
// Only one #statusModal on the page
// On click of 'edit' for a row:
//   - Populate #modal-ticket-id, #modal-status, etc, with data from row
//   - Show #statusModal
// On save, submit AJAX as per-ticket logic
```

---

## 6. Summary Table

| Issue                   | Severity | Fix / Change Summary                          |
|-------------------------|----------|-----------------------------------------------|
| Per-use CSRF token      | Medium   | Generate token per form/row                  |
| Modal per row           | Low      | Use single modal, populate via JS            |
| Hardcoded status cell   | Medium   | Use class, not td:nth-child(x)               |
| Error escaping          | Medium   | Escape exception before rendering anywhere   |
| HTML validation         | Low      | Add `required` to modal select               |

---

## 7. Final Notes

- **No major security flaws**, but several scalable, maintainable and best-practices improvements needed.
- **Escaping** and **prepared statements** should be the norm everywhere.
- **Modal HTML** output should not be per row for large data sets.
- **Avoid magic indices** in DOM manipulation. Use classes/data attributes.

---

**For any questions or more refactoring details, contact the reviewer.**