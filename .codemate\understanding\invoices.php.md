# High-Level Documentation: User Invoices Page

## Overview
This PHP script implements a **user invoices page** for an event ticketing platform. The page allows authenticated users to view their invoices for ticket purchases, access specific account settings, and perform related actions, all within a styled dashboard interface.

---

## Main Functionalities

1. **Authentication Required**
   - Only logged-in users can access this page. If a user is not authenticated, they are redirected to the login page.

2. **User Data Retrieval**
   - Fetches the current user's details (profile image, name, email, role) from the database using the user's session ID.

3. **Invoice Retrieval**
   - Retrieves all invoices related to the authenticated user from the database.
   - Each invoice includes details such as ticket code, event title, invoice number, payment date, amount, and payment status (paid, pending, or cancelled).

4. **Admin Check**
   - Determines if the current user has an admin role, potentially for displaying additional options (though not directly used in the main view).

---

## User Interface Structure

### Sidebar Panel
- **User Information Display**
  - Shows the user's profile image or the first initial of their name.
  - Displays user's name and email.
  - Offers a link to edit the user's profile.
- **Navigation Menu**
  - Quick links to: My Tickets, Payment Methods, Invoices (current page), Notifications, Account Preferences, Security, and Logout.
  - Current page (Invoices) is highlighted.

### Main Content Area
- **Invoices Table or Empty State**
  - If there are **no invoices**, shows a message and a link to browse available events.
  - If **invoices exist**:
    - Shows a responsive table listing:
        - Invoice Number
        - Event Name (and ticket code)
        - Payment Date & Time
        - Amount Paid
        - Status (Paid/Pending/Cancelled, each with distinct styling)
        - Action (link to download the invoice)

---

## Localization and Customization

- **Localization**: Text content (labels, messages, etc.) are pulled from a language array (`$lang`) to support multilingual interfaces.
- **Icons**: Uses FontAwesome icons, mapped by helper functions.
- **Styling**: Utilizes Tailwind CSS classes for layout and design consistency.

---

## File Structure and Dependencies

- **Includes/Requires**:
  - Initialization, functions, authentication logic, header, and footer are all included from external PHP files for modularity and reusability.
- **Session Management**:
  - Relies on PHP sessions to manage user state.

---

## Security Notes

- **User Access**: Strict check for authentication before accessing or displaying user-specific data.
- **Database Binding**: Secure queries using prepared statements (bind parameters).
- **Download Links**: Downloads are via a dedicated script, with IDs passed as query parameters.

---

## Extensibility

- The structure allows for easy addition of new sidebar items or customization of the main content.
- Policy and role checks (`$is_admin`) hint at potential future admin functionality.

---

## Summary

**In brief:**  
This page provides authenticated users with a dashboard to view and manage their event ticket invoices, lookup details, download documents, and access related account settings, all within a visually coherent and secure web environment.