# High-Level Documentation

## Overview

This PHP code defines a **Database** connection class that serves as a simple wrapper around the PDO (PHP Data Objects) extension for interacting with a MySQL database. The class streamlines and manages common database operations such as connecting to the database, preparing/executing queries, binding parameters, and handling transactions. It centralizes database access logic, error handling, and configuration.

## Key Features and Functionality

- **Configuration**
  - Uses constants for host, user, password, and database name.
  - Supports persistent connections and sets appropriate PDO options (error handling, fetch mode, emulation mode).

- **Connection Management**
  - Establishes a PDO connection in the constructor.
  - Provides `getConnection()` to access the underlying PDO connection object.

- **Prepared Statements**
  - `query($sql)`: Prepares an SQL statement for later execution.
  - `bind($param, $value, $type)`: Binds a PHP variable to a parameter in the prepared statement with automatic type detection.

- **Query Execution & Result Handling**
  - `execute()`: Executes the prepared statement with error handling.
  - `resultSet()`: Executes the statement and fetches all results as an associative array.
  - `single()`: Executes and fetches a single result record.

- **Meta Information**
  - `rowCount()`: Returns number of rows affected by the last operation.
  - `lastInsertId()`: Returns the last auto-incremented ID from an insert.

- **Transaction Control**
  - `beginTransaction()`: Begins a database transaction.
  - `commit()`: Commits the current transaction.
  - `rollBack()`: Rolls back the current transaction.

- **Error Handling**
  - Errors during connection and execution are logged and optionally output to the user.

## Usage

The class can be instantiated and used as a database helper for various CRUD operations, abstracting away direct PDO usage and providing clear, reusable methods for interacting with the database. It is suitable for use in applications such as ticket management systems or any PHP system requiring robust, reusable database functionality.

---

**NOTE:**  
- The default settings assume a local development environment with no password (`DB_PASS`).  
- Error messages are output and logged, which may not be desirable in production.  
- Only supports MySQL with UTF-8 encoding.  
- Designed for extensibility and maintainability.