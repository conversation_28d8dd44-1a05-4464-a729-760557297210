## High-Level Documentation: User Management Page

### Overview
This PHP script implements an administrative "Manage Users" interface for a web application. It enables admins to view, edit, and delete non-admin users and provides protections and validation to ensure safe and proper operation. The interface includes navigation to other admin sections (events, tickets, discounts, payment cards, etc.) and displays messages about the outcomes of user actions.

---

### Main Features

1. **Page Setup**
   - Sets the page title to "Manage Users".
   - Includes necessary header, footer, and authentication-related files.
   - Ensures only authenticated admin users can access this page (`requireAdmin()`).

2. **User Deletion Workflow**
   - Processes deletion requests via POST submission.
   - CSRF protection enabled through token generation and validation.
   - Prevents deletion of admin users and users who have purchased tickets.
   - Upon successful (or failed) actions, sets appropriate session messages and redirects.

3. **User Listing**
   - Fetches all users from the database, ordered by creation date.
   - Renders users in a responsive table with the following columns:
     - Name
     - Email
     - Phone
     - Role (admin/user)
     - Creation date (formatted)
     - Actions (edit, delete)
   - For each user:
     - If the user is not an admin, provides a delete button that opens a confirmation modal.
     - Provides an "edit" button for all users.

4. **Navigation Panel**
   - Panel of buttons allow navigation to other administrative sections:
     - Dashboard
     - Events
     - Users (this page, highlighted)
     - Tickets
     - Discounts
     - Payment Cards
     - Back to main site

---

### Security and Best Practices

- **Authorization:** Only admins can access and perform operations on this page.
- **CSRF Protection:** Every destructive action requires a valid CSRF token to prevent cross-site request forgery.
- **Role Protection:** Cannot delete admin users via the interface.
- **Business Logic Protection:** Cannot delete users associated with tickets, preserving referential integrity.
- **Safe Output:** User-provided data is safely output using `htmlspecialchars()`.

---

### User Interface

- **Bootstrap-based Layout:** Responsive HTML layout using Bootstrap classes and components.
- **Actionable Modals:** Deletion requires confirmation from a modal popup.
- **Feedback:** Uses session variables to display error or success messages on redirection.

---

### Extensibility

- **Multi-Language Support:** Uses a `$lang` array for language-specific labels, facilitating translation.
- **Centralized Auth and Utility Functions:** Handles authentication, date formatting, CSRF, and reusability via includes and required files.

---

### File Dependencies

- `includes/admin_header.php`, `includes/admin_footer.php`: For consistent page layout.
- `../includes/auth_functions.php`: For authentication, authorization, and CSRF functions (e.g., `requireAdmin()`, `generateCSRFToken()`, `verifyCSRFToken()`).
- Language labels and formatting helpers.

---

**Summary:**  
This script is a secure, fully-featured admin page that enables safe management of user accounts, enforcing crucial business and security rules, and adhering to good web application development practices.