# High-Level Documentation

## Overview

This PHP code provides a backend for creating cryptocurrency payment invoices using the CryptoBot API. Its primary goal is to allow users to generate payment requests (invoices) and test the connection to the CryptoBot payment service. It is intended to be used in e-commerce scenarios or any system where payments in crypto (specifically USDT, but can be modified) are needed.

---

## Main Features

### 1. Create Payment Invoice

- **Function:** `createInvoice($amount, $order_id, $description = '')`
- Converts a given payment amount (in ILS, Israeli Shekel) to USD.
- Prepares required parameters according to CryptoBot's invoice requirements.
- Supports adding an optional "return button" after payment, with various button behaviors (e.g., callback, open bot, etc.).
- Sends a POST request to CryptoBot's `/createInvoice` endpoint with the necessary headers and parameters using CURL.
- Handles response parsing, error detection, and debugging info logging.
- Adds robust error logging and returns detailed error messages in case of failures, along with user tips if DNS/host resolution issues are detected.
- Records debug information into a log file for future troubleshooting.
- Returns the invoice details (success, message, pay URL, etc.) as an array.

---

### 2. Test API Connection

- **Function:** `testApiConnection()`
- Sends a GET request to CryptoBot's `/getMe` API endpoint to verify if the provided API token works and the server is reachable.
- Handles errors and parses responses, providing structured feedback and debug information.

---

### 3. AJAX and Direct Access Handling

- The code supports AJAX POST requests to `create_invoice` (to initiate new invoices) and `test_connection` (to test API connectivity).
- Validates required POST data and returns responses in JSON format suitable for front-end consumption.
- If accessed directly with `?test=1` in the URL (GET request), it prints out the API test result for manual diagnostics.

---

## Configuration

- Loads settings from an external `config.php` file (must include at least the API token and USD/ILS exchange rate).
- API Token is used in authorization headers for all outgoing requests.

---

## Error Handling / Diagnostics

- All CURL requests provide extended diagnosis, including HTTP status codes, CURL error messages, timings, server and client IPs, etc.
- Writes detailed logs to a file (`debug_log.txt`) for every API interaction.
- User-facing errors try to give actionable advice (especially in the case of network/DNS errors).

---

## Security & Usage Notes

- Disables SSL peer verification for CURL (not recommended in production due to security risks).
- Expects to be protected behind access control on the server-side (since direct POST requests can trigger sensitive operations).
- Handles both GET and POST input for some parameters for flexibility in usage (e.g., via web forms or AJAX).

---

## Customization & Extensibility

- The code can easily be extended to support other cryptocurrencies supported by CryptoBot (`USDT`, `BTC`, `ETH`, `TON`).
- Currency conversion logic can be adapted for other currencies.
- The invoice creation routine allows customization of button labels and actions.

---

## External Dependencies

- Requires `config.php` to exist with relevant configuration.
- Needs CURL extension enabled in PHP.
- File write permissions for logging.

---

## Intended Audience

- Developers integrating CryptoBot as a payment gateway in a PHP-based web application, shop, or service.
- Admins needing to diagnose crypto payment gateway issues via API tests.

---

## Example Usage

Send an AJAX POST request to this script with:
- action: `create_invoice`
- amount, order_id, description

**Or** with action: `test_connection` to validate API connectivity.

---

This code abstracts the complexities of dealing with the CryptoBot API, handles currency conversion, error-logging, and returns concise, user-friendly, and debug-rich responses for building a robust payment workflow.