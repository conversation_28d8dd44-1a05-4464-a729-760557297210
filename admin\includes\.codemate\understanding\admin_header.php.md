## High-Level Documentation

### Overview
This PHP code provides the header and navigation template for an Admin Panel of a web application. It initializes important UI and backend elements such as database connections, multi-language support, session message handling (success/error), and a Bootstrap-powered navigation bar. It ensures consistent appearance and basic functionality across admin pages.

---

### Key Components

1. **Session & Dependencies**
    - Assumes a session has already started (likely elsewhere, e.g., `init.php`).
    - Loads essential backend files:
        - Database connection configuration.
        - Utility and authentication functions.
        - Price formatting logic.

2. **Language Support**
    - Determines the current language from the session or defaults to English.
    - Loads the appropriate language file with translations.
    - Sets the HTML text direction (`ltr` or `rtl`) based on language.

3. **Database**
    - Initializes a PDO database connection for further queries throughout the admin pages.

4. **Session Messages**
    - Reads and then clears any flash messages (success & error), so they're shown only once.
    - Displays these messages as Bootstrap alerts.

5. **HTML Template Output**
    - <PERSON>gins standard HTML5 output.
    - Injects language and text direction attributes in the `<html>` tag.

6. **Assets**
    - Loads common assets via CDN:
        - Bootstrap (including RTL support if needed)
        - Font Awesome icons
    - Loads two custom CSS files for further styling.

7. **Inline Styles**
    - Defines additional styling for dashboard cards.

8. **Navigation Bar**
    - Built with Bootstrap.
    - Provides links to key admin pages: Dashboard, Events, Users, Tickets, Discounts, Payment Cards, Login Logs, and Contact Messages.
    - Includes two dropdowns:
        - **User Account:** Links to main site and logout.
        - **Language Selector:** Allows users to switch among English, Arabic, and Hebrew (with redirection to the current page after switching).

9. **Content Area**
    - Displays session messages if present.
    - Leaves a `<main>` section open for subsequent page-specific content.

---

### Typical Usage

- **Include as a Header:**  
  This template is meant to be included at the start of admin panel pages, before any page-specific content.
- **Consistent Admin Experience:**  
  Ensures consistency in navigation, styling, language, and messaging across the admin section.
- **Localization & Accessibility:**  
  Supports LTR/RTL and multiple languages with easy switching.

---

### Security & Best Practices

- Session variables and user inputs are properly escaped.
- Pages are intended to run for authenticated users with admin access (though actual checks occur elsewhere).
- Designed to be modular with central inclusion of assets, language, and message functionality.

---

**Summary:**  
This code is a modular PHP template providing the administrative interface’s header, navigation, base styling, localization support, flash messaging, and main content area container for a web application's admin panel.