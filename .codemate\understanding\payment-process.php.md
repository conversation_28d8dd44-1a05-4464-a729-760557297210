# High-Level Documentation: Event Ticket & Transport Booking + Payment Processing (PHP)

## Overview

This PHP script handles the **checkout and payment process for event ticket reservations**, with support for optional **transport (shuttle) booking**. It includes card payment validation, ticket and order creation, recording of technical/session info, notification triggers, and secure data handling.

---

## Core Functionalities

### 1. **Authentication & Access Control**
- **User must be logged in**: <PERSON>ript checks session before continuing.
- **Access method**: Only POST requests with an event ID are processed; invalid requests are redirected.

### 2. **Input Management & Validation**
- **Receives**:
  - `event_id` (required)
  - `quantity` (optional, default 1)
  - Card info: number, expiry, CVV
  - Optional `with_transport` flag and transport-related booking data (if in session)
- **Sanitizes & validates** credit card number (length and Luhn algorithm).
- **Checks for existence of requested event** in the database.

### 3. **Booking Calculation**
- If **transport is booked** and a **combined event+transport booking** exists in the session, uses its pre-calculated total.
- Otherwise, computes price: event price × ticket quantity.

### 4. **Technical Details Recording**
- **Gathers**: User agent, browser, OS, device type, IP address.
- **Records** main booking action in logs (for auditing).

### 5. **Database Processing**
- **Main Order Processing** (`process_payment()`):
  - Saves order & generates unique order ID.
  - Creates ticket codes for each ticket and saves them.
- **Transport Booking** (`create_transport_booking_direct()`):
  - Inserts new transport booking into the DB, ensures trip exists, generates unique booking code.
- Handles DB errors gracefully, logs for admin review.

### 6. **Secure Payment Info Handling**
- Only the **last 4 digits of the card number** are stored (masked, as per PCI compliance).
- **Does NOT store CVV** in clear text; hashes for illustrative purposes (shouldn’t store at all in real-world apps).
- Payment details + technical info logged in a `payment_cards` table.
- Sensitive data is **never stored in session or database in full**, following best practices.

### 7. **Notifications & User Feedback**
- Success/failure triggers notifications via dedicated notification functions (DB or session).
- Different scenarios:
  - Direct ticket booking
  - Combined ticket/transport booking
  - Transport-only booking (for event ticket holders)
- Notification messages are customized accordingly.
- Errors in payment/transport booking are captured and notified to the user.

### 8. **Session State Handling**
- Stores booking, payment, and notification data in session for reuse or confirmation pages.
- Clears one-time session data (like intermediate booking info) after successful operations.

### 9. **Redirection & Error Handling**
- On success, redirects to payment processing/confirmation page.
- On failure, stores error messages and redirects user back to the checkout form.
- Logs all errors internally for admin/support review.

---

## Security / Compliance

- **Sensitive Card Info**: Strictly limited and masked.
- **CVV Should not be stored** (currently hashed for demo, not real-world use).
- **Validation**: Both client-side and server-side validation of card inputs.
- **Technical Fingerprinting**: Captures device/browser/OS for fraud detection & analytic purposes.

---

## Extensible Integration Points

- **Database Abstraction**: Uses `Database` class; can easily adapt for different DB engines.
- **Notifications**: Pluggable via `notification_functions.php` for in-app/email/SMS alerts.
- **Modular Functions**: Payment and transport logic separated for maintainability.

---

## Summary Flow

1. **Validate Request & Authentication**
2. **Sanitize & Validate Inputs**
3. **Determine Booking Type and Amount (considering transport if needed)**
4. **Log transaction attempt and collect device data**
5. **Create order and tickets, or just tickets as appropriate**
6. **Store masked payment info**
7. **Trigger user/admin notifications**
8. **Create transport booking if needed**
9. **Redirect user to appropriate confirmation page**
10. **Handle/Log errors and display user-friendly messages on failure**

---

## Usage Context

- **Online event ticketing system** with optional shuttle/transport logistics.
- **Supports credit card payments** with secure data handling.
- User experience is prioritized by immediate feedback and clear paths upon error/success.

---

**Note:** 
- This script should be used in a secure, PCI-compliant server environment.
- Actual payment gateway integration and security reviews are required for live deployments.