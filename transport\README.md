# لوحة تحكم المواصلات

## نظرة عامة

لوحة تحكم شاملة لإدارة نظام المواصلات والحجوزات. تم تطويرها باللغة العربية مع دعم كامل للاتجاه من اليمين إلى اليسار (RTL).

## الميزات الرئيسية

### 📊 نظرة عامة
- عرض الإحصائيات الرئيسية (الإيرادات، الرحلات، السائقين، الحجوزات)
- آخر الأنشطة والحجوزات
- ملخص الأداء العام

### 💰 إدارة الإيرادات
- تتبع الإيرادات الإجمالية
- تصنيف الإيرادات حسب نوع وسيلة النقل
- إحصائيات مفصلة للمبيعات

### 📍 نقاط الانطلاق
- إدارة نقاط الانطلاق المختلفة
- تصنيف حسب المناطق (شمال، وسط، جنوب)
- حالة النشاط لكل نقطة

### 🚌 إدارة الرحلات
- عرض جميع الرحلات المتاحة
- تفاصيل المسارات والأوقات
- إدارة المقاعد المتاحة
- ربط الرحلات بالفعاليات

### 👨‍💼 إدارة السائقين
- ملفات شخصية للسائقين
- معلومات المركبات
- حالة السائق (متاح، مشغول، غير متصل)
- تقييمات وخبرات السائقين

### 📋 إدارة الحجوزات
- عرض جميع الحجوزات
- تفاصيل العملاء والمدفوعات
- حالات الحجز المختلفة
- طرق الدفع المتنوعة

### 📈 التحليلات
- أكثر نقاط الانطلاق استخداماً
- إحصائيات طرق الدفع
- ملخص الأداء العام

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## قاعدة البيانات

### الجداول المطلوبة:
- `transport_bookings` - الحجوزات
- `transport_trips` - الرحلات
- `transport_drivers` - السائقين
- `transport_vehicles` - المركبات
- `transport_types` - أنواع وسائل النقل
- `transport_starting_points` - نقاط الانطلاق
- `events` - الفعاليات
- `users` - المستخدمين

## التثبيت

1. تأكد من وجود قاعدة البيانات وتشغيل ملف `setup_transport_types.php`
2. ضع الملفات في مجلد `transport/`
3. تأكد من صحة إعدادات قاعدة البيانات في `config/database.php`
4. افتح `dashboard.php` في المتصفح

## الاستخدام

### الوصول للوحة التحكم
```
http://yoursite.com/transport/dashboard.php
```

### التنقل
- استخدم القائمة العلوية للتنقل بين الأقسام المختلفة
- كل قسم يعرض البيانات الحقيقية من قاعدة البيانات
- يمكن إضافة عناصر جديدة من خلال الأزرار المخصصة

### الأقسام المتاحة:
1. **نظرة عامة** - الصفحة الرئيسية مع الإحصائيات
2. **الإيرادات** - تفاصيل المبيعات والأرباح
3. **نقاط الانطلاق** - إدارة المواقع
4. **الرحلات** - إدارة الرحلات والمسارات
5. **السائقين** - إدارة فريق السائقين
6. **الحجوزات** - متابعة طلبات العملاء
7. **التحليلات** - تقارير وإحصائيات مفصلة

## التخصيص

### الألوان والتصميم
- يمكن تعديل الألوان من خلال متغيرات Tailwind CSS
- ملف `dashboard-rtl.css` يحتوي على تحسينات إضافية

### إضافة ميزات جديدة
- أضف أقسام جديدة في HTML
- أضف دوال JavaScript للتنقل
- أضف استعلامات قاعدة البيانات حسب الحاجة

## الأمان

- تأكد من تشفير كلمات المرور
- استخدم Prepared Statements لمنع SQL Injection
- قم بتطبيق صلاحيات المستخدمين
- استخدم HTTPS في الإنتاج

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور خصيصاً لنظام إدارة المواصلات والحجوزات.

---

**ملاحظة**: تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل إجراء أي تعديلات.
