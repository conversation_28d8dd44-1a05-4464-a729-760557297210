# High-Level Documentation: "Add Event" Admin Page

## Purpose

This code implements an **admin interface for adding new events** to a system, such as an event management website or ticketing platform. The event information is collected via a form and submitted asynchronously (AJAX) to be processed server-side.

---

## Major Functionalities

### 1. **Authentication & Access Control**
- **Admin-only access:** The page enforces that only authenticated admin users can access the event creation form.
- **CSRF Protection:** A CSRF token is generated for form submission to protect against cross-site request forgery attacks.

### 2. **Form Display**
- **Form Fields:** The form captures all relevant event information:
  - Event title (required)
  - Description
  - Start/End date and time (start required, end optional)
  - Location (required)
  - Original and discounted (current) prices
  - Category/Type (required)
  - Number of available tickets (required)
  - Event image (optional)
  - “Featured” flag (optional)
- **Field Validations:** Required fields are enforced using the `required` attribute.

### 3. **Image Upload and Preview**
- **Image Preview:** When an image is chosen, a preview is displayed immediately via JavaScript before form submission.

### 4. **AJAX Form Submission**
- **Asynchronous Processing:** When the admin clicks the save button, form data (including files) is sent to `add_event_ajax.php` via JavaScript Fetch API (AJAX), without a page reload.
- **User Feedback:** While saving, a loading indicator is shown; on success, a success message is displayed, and the admin is redirected to the events list page; on failure, an error is shown.

### 5. **User Experience**
- **Bootstrap Components:** The UI leverages Bootstrap classes for layout, form controls, grids, and feedback alerts.
- **Language Support:** Label texts are internationalized via a `$lang` array (not shown in this snippet), indicating the platform supports multiple languages.

### 6. **Error Handling**
- **PHP Error Reporting:** Enabled at the top for debugging during development.
- **Graceful Failure:** If includes (auth/functions) fail to load, a styled error message is displayed.
- **JavaScript Errors:** If AJAX fails, the admin is notified.

### 7. **Code Modularity**
- **Header/Footer Includes:** Common header and footer files are included for consistency.
- **PHP Logic and AJAX Handler Separation:** All actual event creation logic (database interactions, validation) is handled in a separate `add_event_ajax.php` file.

---

## Typical Workflow

1. **Admin navigates to the "Add Event" page.**
2. **Form is displayed with empty fields and possibly language-specific labels.**
3. **Admin fills in the details, optionally picks an image (with preview).**
4. **Clicks “Save Event”; form is sent asynchronously to the server.**
5. **Message indicates success or error; successful creation redirects to event list.**

---

## Security Considerations

- **CSRF token is used** to prevent forgery in AJAX submissions.
- **Page access is restricted to admins** (`requireAdmin()`).
- **Error reporting enabled** only for development (should be disabled on production).

---

## What’s Not Covered Here

- The **server-side handler** `add_event_ajax.php` is not shown—actual validation/storage is handled there.
- The **$lang array** used for localization is not included.
- No listing of event editing; this is solely for event creation.

---

## Summary

This script is a well-structured, secure, admin-facing UI page for adding events, featuring AJAX-based submission, image preview, and robust feedback mechanisms. It is intended to be part of a larger admin panel for managing events.