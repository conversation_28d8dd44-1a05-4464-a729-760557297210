# High-Level Documentation: Manage Events Admin Page

## Overview

This PHP code implements an **admin interface** for managing events in a web application. It is responsible for displaying a list of all events, as well as providing admin users with the ability to add, view, edit, and delete events through a web-based dashboard.

---

## Main Features

### 1. Access Control

- **Admin Verification**: Only users verified as administrators can access this page (`requireAdmin()`).
- **CSRF Protection**: Actions that modify event data (like deletion) require a CSRF token.

### 2. Event Listing

- **Events Table**: Fetches all event records from the database, displaying important details in a table (title, date, location, price, available tickets, "featured" status, and "active" status).
- **Readability**: Utilizes formatting functions for dates and prices.
- **Icons/Badges**: Indicates boolean states (e.g., "featured", "active") visually with icons and badges.
- **Empty State**: If no events exist, a row indicates "no events found".

### 3. Event Actions

For each event, admins can:

- **View**: Opens the public event page in a new tab.
- **Edit**: Navigates to the event editing form.
- **Delete**:
  - Shows a Bootstrap modal dialog to confirm deletion.
  - Uses AJAX to send a secure deletion request to the server-side endpoint (`delete_event_ajax.php`).
  - On success, removes the event's row from the UI and shows a confirmation message.
  - Handles and displays errors appropriately if deletion fails.

### 4. Admin Navigation

- Provides a set of navigation buttons for various admin sections (Dashboard, Events, Users, Tickets, Discounts, Payment Cards, and link back to the public site).

### 5. Structure & UI

- Implements a **Bootstrap-based responsive layout** — using cards, tables, buttons, and modals for a user-friendly admin experience.
- Language strings (`$lang`) are used for all user-facing text, supporting localization/multilingual sites.

---

## Code Summary

- **Dependencies**: Includes shared headers/footers, language system, and authentication helpers.
- **Database Access**: Queries the events table, handles errors gracefully.
- **Security**: CSRF token used for AJAX actions; admin access enforced.
- **Client-side Interactivity**: JavaScript manages modal interactions, user feedback, and dynamic DOM updates after delete operations.

---

## Extensibility

- **Delete logic** is outsourced to a dedicated AJAX handler (`delete_event_ajax.php`), promoting clean separation of logic.
- **Localization**: All displayed text can be translated via the `$lang` array.
- **Customizable navigation** for other relevant admin sections.

---

## Intended Users

- **Administrative staff only** — controls and security are in place to prevent unauthorized access or manipulation.

---

## Typical Use-Case Workflow

1. Admin logs in and accesses the Events management page.
2. Admin can:
    - Add a new event.
    - Click to view or edit existing events.
    - Remove an event with in-place confirmation and immediate feedback.
3. Navigation to other admin areas is facilitated via sidebar buttons.

---

## Summary

This script serves as a central "Manage Events" dashboard for site administrators, balancing usability, performance, and security while managing the event database. It uses modern patterns like AJAX for seamless interactivity and includes security best practices for authenticated operations.