# High-Level Documentation: Admin Login Script

## Purpose
This PHP script implements the backend and frontend for the **Admin Login** page of a web application's admin panel. Its main goals are to:
- Authenticate admin users
- Restrict access to the admin panel to only authorized users
- Display login errors or relevant messages
- Provide a styled login form

## Main Features

### 1. Session Management & Initialization
- Ensures a PHP session is started for user/login state tracking.
- Loads required helper files for authentication, generic utilities, and database connection.

### 2. Access Control
- Redirects users **already logged in as admin** straight to the admin dashboard (index.php).
- Otherwise, presents the login form.

### 3. Login Processing (POST Handling)
- Accepts login form submissions (`email` and `password`).
- Sanitizes and validates user input.
- Authenticates user credentials via `loginUser()`.
- If login succeeds:
    - Checks if the authenticated user has 'admin' role.
    - Grants access and sets a success message if the user is admin.
    - Refuses access (logs out and shows error) if not an admin.
- Displays error messages on authentication failure.

### 4. Frontend (HTML & CSS)
- Presents a Bootstrap-styled login form.
- Shows contextual error/success messages above the form.
- Provides navigation back to the main website.

### 5. Visual Design
- Uses Bootstrap and Font Awesome for modern styling and iconography.
- CSS ensures form is centered and visually distinct.

## Security Notes
- Sanitizes email input.
- Ensures only users with the 'admin' role can access the admin panel.
- Handles login/logout and error message display securely.

## Integration Points
- Depends on external PHP includes for database operations, authentication, and utilities.
- Uses a MySQL (or compatible) database for credential verification.
- Designed to integrate with multi-role user systems where 'admin' role is required for the admin panel.

---

**Summary:**  
This script provides a secure, styled login interface and logic, allowing only admin users to access the admin panel while handling input validation, session management, and user feedback gracefully.