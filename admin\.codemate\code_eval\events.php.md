# Code Review Report

**File:** events.php  
**Purpose:** Admin event management interface (listing, deleting events, navigation)

---

## General Observations

- Follows separation of concerns for HTML/PHP/JS.
- CSRF token generation and AJAX deletion implemented.
- Uses prepared statements for fetching events (PDO).
- There are a few practices and implementation concerns that merit improvements.

---

## Issues, Recommendations, and Patch Suggestions

### 1. **Potential Variable Initialization (PDO Instance)**

**Issue:**  
`$pdo` is used without initialization or global import.  
**Recommendation:**  
Ensure `$pdo` is declared (`global $pdo;` or explicitly required via config).

**Suggested change:**
```php
// Before using $pdo
require_once '../includes/db_connection.php'; // or your real DB config file
```
_or if inside a function_
```php
global $pdo;
```

---

### 2. **Security: SQL Query (Unused/Hardcoded Logic)**

**Issue:**  
Directly selecting `*`, and creating arbitrary `is_active = 1` in SELECT.  
This means is_active is **always** set to 1, giving misleading UI signals.

**Suggested change:**
```sql
SELECT *,
       featured AS is_featured,
       is_active
  FROM events
ORDER BY date_time DESC
```
_(Assuming an `is_active` field exists in the table. If not, remove the UI column or use correct logic for active/inactive.)_

---

### 3. **Array Fetch Mode (Best Practice)**

**Issue:**  
`$stmt->fetchAll();` could return result as indexed+associative arrays.  
**Recommendation:**  
Fetch as associative array for clarity.

**Suggested change:**
```php
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);
```

---

### 4. **Language Variable Initialization**

**Issue:**  
No guarantee that `$lang` is populated before usage.

**Recommendation:**  
Ensure `$lang` is loaded by including a language file or adding a check.

**Suggested change:**
```php
require_once '../includes/lang.php'; // or wherever $lang is set
if (!isset($lang)) { $lang = []; }
```

---

### 5. **CSRF Token: Unique Per Event Delete?**

**Note:**  
The same CSRF token is reused for every delete action, making it easier to brute-force. **(Not necessarily wrong, but per-action tokens are stronger, or simply include one in a hidden form.)**

---

### 6. **XSS Protection**

**Good:**  
Escaping most dynamic content via `htmlspecialchars` is correct.  
**Concern:**  
A few spots directly outputting values (like `<?php echo $event['available_tickets']; ?>` and `<?php echo formatDate($event['date_time']); ?>`).  
If `available_tickets` is integer or float, no issue. If not, do:

**Suggested change:**
```php
<?php echo htmlspecialchars($event['available_tickets']); ?>
```
_Only needed if type is not guaranteed numeric._

---

### 7. **Error Handling for HTML-In-JS (Translation)**

**Issue:**  
When inserting an alert via JavaScript (after deletion), the translation string is rendered into the JS code.  
But: `<?php echo $lang['no_events_found']; ?>` will be embedded once, when PHP runs, so is correct -- unless $lang can be user-modified, XSS unlikely.  
Still, preferable to pass translations via JS object for complex multilingual code.

---

### 8. **Bootstrap Modal Instance Retrieval**

**Issue:**  
`bootstrap.Modal.getInstance(this.closest('.modal'))`  
**Problem:**  
If the modal hasn't been initialized manually by Bootstrap JS (rare, but possible), `getInstance` may return null.  
**Suggested improvement (robust fallback):**
```javascript
const modalEl = this.closest('.modal');
let modal = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
```

---

### 9. **Graceful Deletion Row Removal**

**Issue:**  
After row removal, check for remaining children:  
```js
if (tableBody.children.length === 0) { ... }
```
**Issue:**  
If table contains whitespace or invisible nodes, may miscount.  
**Improvement:**  
Count only rows representing events.

**Suggested change:**
```javascript
if (tableBody.querySelectorAll('tr').length === 0) {
    //...
}
```

---

### 10. **HTTP Method for Deletion**

Using POST for deletion is correct.

---

### 11. **Unused/Disabled Admin Navigation Button**

**Observation:**  
"Payment Cards" link does not use `$lang`, but others do.

**Suggested change:**
```php
<?php echo $lang['payment_cards']; ?>
```
(Add to your language file if missing)

---

### 12. **Function Definitions (formatDate, formatPrice)**

**Recommendation:**  
Ensure these helper functions are defined/required before use.

---

## Summary Table

| Issue                        | Suggested Change/Correction (Pseudo Code)                               |
|------------------------------|-------------------------------------------------------------------------|
| Uninitialized `$pdo`         | `require_once '../includes/db_connection.php';`                         |
| is_active logic error        | `SELECT *, is_active, featured AS is_featured FROM events ...`           |
| Array fetch mode             | `$events = $stmt->fetchAll(PDO::FETCH_ASSOC);`                          |
| Language initialization      | `require_once '../includes/lang.php';`                                  |
| JS Modal robust fallback     | `let modal = bootstrap.Modal.getInstance(modalEl) \|\| new bootstrap.Modal(modalEl);`|
| Row removal: 0-event check   | `if (tableBody.querySelectorAll('tr').length === 0) { ... }`            |
| Payment Cards localization   | `<?php echo $lang['payment_cards']; ?>`                                 |
| Numeric value escaping       | `<?php echo htmlspecialchars($event['available_tickets']); ?>`           |

---

## Sample Pseudocode Corrections

```php
// Replace query
$stmt = $pdo->query("
    SELECT *, 
           is_active, 
           featured AS is_featured
      FROM events
    ORDER BY date_time DESC
");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);
```

```php
// Ensure $pdo is defined
require_once '../includes/db_connection.php'; // or your db file
```

```php
// Ensure $lang is defined
require_once '../includes/lang.php';
if (!isset($lang)) { $lang = []; }
```

```php
// Payment Cards localization
<a href="payment_cards.php" class="btn btn-outline-primary w-100 mb-2">
    <i class="fas fa-credit-card"></i><br>
    <?php echo $lang['payment_cards']; ?>
</a>
```

```javascript
// JS Modal instantiation robust
const modalEl = this.closest('.modal');
let modal = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
```

```javascript
// Check if event table is empty (for row removal)
if (tableBody.querySelectorAll('tr').length === 0) {
    // Append the no-events row
}
```

---

## Final Recommendations

- Refactor hardcoded logic for is_active to match database values.
- Check for initialization of all required resources (`$pdo`, `$lang`).
- Add additional output escaping when there is any uncertainty over data origin.
- Ensure language keys are present and not hardcoded for all translatable content.
- Consider robustness and fallback in JavaScript interacting with dynamic UI components.

---

**End of Review**