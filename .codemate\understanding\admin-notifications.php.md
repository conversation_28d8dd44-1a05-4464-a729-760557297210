# High-Level Documentation: Admin Notification Management System (PHP)

## Overview
This code implements an **Admin Notification Management Panel** for a web application. The panel enables administrators to:

- View notification statistics and recent notifications.
- Send new notifications to all users or selected active users.
- Manage notification delivery, preview, and activity logging.

## Main Functionalities

### 1. **Access Control and Initialization**
- **Authentication:** Ensures the user is logged in and has admin notification management permissions.
- **Includes:** Pulls in required files for authentication, utilities, database access, and notification/admin functions.


### 2. **Notification Sending (POST Processing)**
- **Form Submission Handling:** Processes a submitted notification form.
- **CSRF Protection:** Validates CSRF tokens for security.
- **Input Validation:** Checks for the presence of required fields (`title`, `message`).
- **User Targeting:**
  - All users: Broadcasts notification to all active users.
  - Selected users: Sends to a subset, with user IDs passed and sanitized.
- **Notification Functions:** Calls backend helpers to deliver notifications.
- **Activity Logging:** Logs admin actions for accountability.
- **Success/Error Feedback:** Provides admin with status messages after each action.


### 3. **Statistics and User Data Fetching**
- **Database Access:** Uses a `Database` class to:
  - Retrieve all active users for the notification target list.
  - Fetch notification statistics (total, unread, number of users with notifications).


### 4. **UI Rendering**
- **Statistics Display:** Shows a summary of notifications and users using styled components.
- **Notification Form:**
  - Inputs for title, message, optional link, and target users.
  - Option to toggle between “all users” and “custom selection.”
  - Modal form preview before sending notification.
- **Recent Notifications List:** Shows a table/list of the latest admin notifications, recipient, time, and read status.


### 5. **Client-Side Features (JS)**
- **User Selection Toggle:** Shows/hides the custom user selection area based on dropdown.
- **Preview Modal:** Allows the admin to preview the notification before sending.
- **UX Enhancements:** Modal close behavior and simple frontend validation.


### 6. **Styling and Componentization**
- Utilizes Tailwind CSS (or similar) for layout and design.
- Consistent styling for alerts, inputs, notifications, and dashboard blocks.


## Security and Best Practices

- **Authentication + Authorization:** Only logged-in admins can access.
- **Input Sanitization:** Inputs are sanitized before use.
- **CSRF Protection:** Ensures form posts are legitimate.
- **Activity Logging:** Records all notification-sending actions for auditing.

## Database Structure (Implied)
- **Users Table:** With fields like `id`, `name`, `email`, `status`.
- **Notifications Table:** Stores per-user notifications, including `title`, `message`, `user_id`, `is_read`, `created_at`.

---

## Summary

This code module provides a secure, robust admin interface for sending and tracking in-app notifications, complete with analytics, user targeting, and modern UI/UX features. It uses best practices for security, separation of concerns, and responsive design.