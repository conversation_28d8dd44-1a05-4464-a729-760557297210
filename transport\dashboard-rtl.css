/* تحسينات إضافية للوحة التحكم باللغة العربية */

/* تحسين الخطوط العربية */
body {
    font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين التنقل */
.nav-item {
    transition: all 0.3s ease;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 3px solid white;
    font-weight: bold;
}

/* تحسين البطاقات */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* تحسين الجداول */
table {
    border-collapse: separate;
    border-spacing: 0;
}

table th {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    color: white;
    font-weight: 600;
    text-align: right;
    padding: 12px 16px;
}

table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

table tr:hover {
    background-color: #f9fafb;
}

/* تحسين الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #4c1d95 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.1);
}

/* تحسين الأيقونات */
.icon-container {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين الحالات */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

/* تحسين النماذج */
.modal-content {
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.form-input {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    outline: none;
}

/* تحسين الرسوم المتحركة */
.dashboard-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.dashboard-section.active {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .nav-item {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .stats-card {
        padding: 16px;
    }
    
    table {
        font-size: 14px;
    }
    
    .modal-content {
        margin: 16px;
        max-width: calc(100% - 32px);
    }
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسين التركيز */
*:focus {
    outline: 2px solid #7c3aed;
    outline-offset: 2px;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(135deg, #7c3aed 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الظلال */
.shadow-custom {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-custom-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
                0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
