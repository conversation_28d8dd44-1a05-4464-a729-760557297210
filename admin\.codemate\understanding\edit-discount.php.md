# High-Level Documentation: Edit Discount Page

## Overview

This PHP script implements the administrative interface for editing a discount ("coupon") in a web application. It is intended for admin users and supports AJAX-based editing of individual discount properties such as the code, type, value, and limitations.

---

## Key Features

- **Security & Authentication**
  - Checks for a valid discount ID; redirects if not found.
  - Only accessible to admin users (requires admin authentication).
  - Implements CSRF protection via a generated token.

- **Data Retrieval**
  - Fetches discount/coupon details from the database using the provided `id`.

- **UI Rendering**
  - Renders a form pre-filled with current discount data, including:
    - Discount code, type (percentage or fixed), value, usage limits, expiration date, and (readonly) usage count.
  - UI is internationalized via the `$lang` array for labels.
  - Provides navigation back to the main discounts list.

- **Form Handling**
  - No form submission is performed on page reload.
  - Edit is handled via JavaScript using an AJAX call to an endpoint (`update_discount.php`).

- **Dynamic UI Behavior**
  - The value input suffix changes dynamically (`%` or `₪`) based on selected discount type.
  - Status messages are displayed dynamically based on the result of the AJAX request.

- **Post-Submission Behavior**
  - On successful update, shows a success message and then redirects to the discounts list.
  - On error, displays an error message.

---

## High-Level Flow

1. **Access Control**: Validates presence of a discount ID and verifies admin privileges.
2. **Data Fetching**: Retrieves the current discount data from the database.
3. **Page Rendering**: Shows the editable form populated with the coupon's details.
4. **Editing via AJAX**:
    - When "Save" is clicked, form data is sent asynchronously.
    - Handles CSRF token/checks.
    - Status and error messages are displayed inline.
    - Successful edits redirect back to the main discount administration page.

---

## Notable Implementation Details

- **No direct form submission**: All updates happen asynchronously (AJAX).
- **Read-only usage count**: Displayed but not editable.
- **Field Hints**: Usage limit and expiration date fields provide user-friendly instructions.
- **No direct manipulation of `is_active`**: Not present in the form or in the database table.

---

## Dependencies

- PHP (with PDO for database interaction)
- Bootstrap/CSS Framework for styling
- JavaScript (for AJAX and client-side UI updates)
- Language translation array (`$lang`) for internationalization
- Additional includes for headers/footers and authentication logic

---

## Related Files

- `discounts.php`: Main page listing all discounts.
- `includes/admin_header.php` & `includes/admin_footer.php`: Common page layout for the admin area.
- `../includes/auth_functions.php`: Authentication and admin guard logic.
- `update_discount.php`: Receives and processes the AJAX edit requests; expected to return JSON.

---

## Security Considerations

- Admin authentication enforced.
- CSRF protection via token.
- Server-side validation expected on the AJAX endpoint.

---

## Extensibility

- Could be extended to support toggling active/inactive state, enhanced validation, or richer auditing.

---

**In summary:**  
This script provides an admin-only, AJAX-powered web form for editing discount details securely and efficiently, with a focus on usability and security best practices.