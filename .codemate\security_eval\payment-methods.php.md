# Security Vulnerability Assessment Report

**File Under Review:** `payment-methods.php`  
**Assessment Date:** 2024-06  
**Scope:** This report **focuses solely on security vulnerabilities** in the provided code.

---

## 1. **Sensitive Data Exposure**

### a. **Credit Card Data Stored in Plaintext**
- Credit card numbers (`card_number`) and CVV (`cvv`) are *stored directly* in the `payment_methods` table in plaintext.
- **Impact:** If the database is compromised, attackers gain direct access to all credit card details, including the card number and CVV.
- **Best Practice:** Payment card details should **never be stored** in plaintext; use tokenization, or if must store (for extreme cases), encrypt with strong encryption and *never store the CVV*.
- **References:** [PCI DSS Requirement 3](https://www.pcisecuritystandards.org/documents/PCI_DSS_v4-0.pdf)

### b. **Passwords in Plaintext**
- The code logs the user password from `$_SESSION['user_password']` in technical info tables and sends it via external integrations.
- **Impact:** Storing or transmitting passwords in plaintext makes users vulnerable to credential theft and secondary breaches.
- **Best Practice:** Do not handle, store, or log plaintext passwords after authentication. Use PHP's `password_hash()` for storage and never expose after that.
- **References:** [OWASP - Sensitive Data Exposure](https://owasp.org/Top10/A06_2021-Vulnerable_and_Outdated_Components/)

### c. **PII Sent via External Services**
- Sensitive data (name, email, card details, technical info, and passwords) is sent to Telegram via `send_telegram_message()`.
- **Impact:** Leaking PII or card info to third parties is a GDPR/PCI violation.
- **Best Practice:** Never send sensitive data over insecure external channels.

---

## 2. **SQL Injection Risks**

### a. **Dynamic SQL in Schema Changes**
- The code dynamically executes SQL statements for `ALTER TABLE`, `SHOW COLUMNS`, and `CREATE TABLE` based on external input or logic.
- **Impact:** While the dynamic parts appear static in this context, using dynamic SQL without validation can be risky, especially if input is used.
- **Best Practice:** Always use parameterized/prepared statements for all queries, limit table/column changes to admin-only logic, and review inputs wrapped into SQL queries.

### b. **GET-based and POST-based SQL Operations**
- User IDs in SQL queries are always bound via PDO (`:user_id`), which is safe.
- `$_GET['delete']` and `$_GET['default']` are checked with `is_numeric()` before passing to queries.
- **Verdict:** No SQL injection present under this usage, but if code changes, this is an area to watch.

---

## 3. **Improper Session Data Management**

### a. **Sensitive Data Stored in `$_SESSION`**
- Stores `user_password`, `paypal_email`, and Paypal verification status in session.
- **Impact:** Anyone with access to session data can retrieve sensitive user information.
- **Best Practice:** Only store minimal session data (user ID, role...), never credentials or sensitive third-party tokens or PII.

---

## 4. **CSRF (Cross-Site Request Forgery) Vulnerabilities**

### a. **Absence of CSRF Tokens**
- No CSRF tokens are present in *any* form submission (`POST`) or for critical GET actions (like `?delete=`, `?default=`).
- **Impact:** Attackers can craft malicious forms/links to trick authenticated users into performing unwanted actions (e.g., deleting payment methods).
- **Best Practice:** Protect **all state-changing actions (POST and GET)** with CSRF tokens.
- **References:** [OWASP CSRF Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html)

---

## 5. **Open Redirection**

### a. **No sanitization of `return_url` parameter for PayPal login redirect**
- `paypal-login.php?return_url=payment-methods.php` is used, which is then redirected upon successful Paypal login.
- **Impact:** If not sanitized/validated in PayPal login handler, could lead to phishing through open redirect. However, this file does a simple static append, so immediate danger is in the implementation of `paypal-login.php`.
- **Best Practice:** Always validate/whitelist allowed redirect URLs on the server side.

---

## 6. **Missing Output Encoding**

### a. **Direct Echoing of User Data**
```php
<img src="<?php echo $user['profile_image']; ?>" alt="<?php echo $user['name']; ?>" ... >
<span class="text-3xl"><?php echo strtoupper(substr($user['name'], 0, 1)); ?></span>
<p class="text-gray-600 text-sm"><?php echo $user['email']; ?></p>
```
- User data is echoed without any escaping or sanitization (e.g., `htmlspecialchars()`).
- **Impact:** Potential for Cross-Site Scripting (XSS) if user info is malicious.
- **Best Practice:** Always HTML-escape all output, especially data from `$user` or DB.

---

## 7. **Insecure Direct Object References (IDOR)**

### a. **Payment Method Modifications via GET**
- `?delete=ID` and `?default=ID` modify data (delete or set default).
- These actions check if the `user_id` owns the `payment_id` before taking action.
- **Impact:** Sufficient as is for IDOR, but see below.
- **Best Practice:** Consider moving **destructive actions to POST requests** instead of GET and combining with CSRF defense.

---

## 8. **Information Leakage**

### a. **Technical Info Logging**
- Collects and stores/checks User-Agent, IP address, device, OS, etc.  
- **Impact:** If technical logs are exposed, profile of user and context is available to attackers.

---

## 9. **Unvalidated Data in HTML Attributes**

### a. **Profile Image URL**
- Prints `$user['profile_image']` directly in `<img src="">` without validation or escaping.
- **Impact:** Allows stored XSS or content injection.
- **Best Practice:** Sanitize URLs and use `htmlspecialchars()`.

---

## 10. **Use of GET for State-Changing Operations**

- Deletion and default change are performed via GET requests.
- **Impact:** Subject to CSRF attacks, unsafe for state changes, and can be triggered by external links.
- **Best Practice:** Use POST and protect with CSRF tokens.

---

## 11. **Potential Race Conditions on Payment Method State**

- After adding a method, another query for `SELECT id FROM payment_methods WHERE user_id = :user_id ORDER BY id DESC LIMIT 1` is used to get the "last added" payment ID.
- **Impact:** If two requests run in parallel (rare but possible), one user's action might affect another's payment method (if e.g., user_id reused, session issues, or DB state not atomic).
- **Best Practice:** Use transaction-safe methods or log last-insert-id when possible.

---

## Recommendations Summary (By Priority)

| Priority | Issue                                                             | Remediation                                                    |
|----------|-------------------------------------------------------------------|----------------------------------------------------------------|
| Critical | Plaintext storage/transmission of card & password data            | Use PCI-compliant methods, irreversible password hashes, and do not store card data or CVV. Remove all PII from external transmission. |
| Critical | No CSRF protection on POST/GET actions                            | Implement CSRF tokens in all forms and state-changing GET links.|
| High     | No output escaping on user data                                   | Use `htmlspecialchars()` on all output to HTML.                |
| High     | Use of GET for destructive actions (delete, set-default)          | Switch to POST and add CSRF defense.                           |
| High     | Unvalidated/sanitized HTML attributes (e.g., `<img src="">`)      | Sanitize and escape URLs for images; reject illegal characters. |
| Medium   | Logging/storing technical info and PII without audit/limitation   | Review what is necessary and protect technical info logs.       |
| Medium   | Storing sensitive data in session (e.g., passwords, PayPal email) | Only store session identifiers and roles, not credentials or PII.|
| Medium   | Technical race on payment method retrieval                        | Use transactional DB operations and/or `lastInsertId()`.        |

---

## Conclusion

**This code is severely susceptible to multiple high-risk security issues, particularly the handling and storage of sensitive user data and lack of standard web security protections.**  
**Immediate action is required to ensure user protection, regulatory compliance (e.g., PCI DSS, GDPR), and prevent exploitation.**

---

> **Do not deploy this code in a production environment until the above issues are remediated.**