# High-Level Documentation: Notifications Page (تذاكر فلسطين)

## Overview
This PHP code implements a **user notifications page** for "تذاكر فلسطين" (Palestinian Tickets), a ticketing platform. The page allows logged-in users to:

- View and manage their notifications (mark as read, delete, bulk mark all as read).
- Configure notification settings by channel (email, SMS) and by notification type (such as event changes or ticket reminders).
- Navigate to other account-related pages via a sidebar.

The code uses Arabic for localization and RTL page direction. The interface is styled using Tailwind CSS and FontAwesome icons.

---

## Major Components & Functionalities

### 1. Session & Access Control
- **Requires Login:** Checks user session; redirects to `login.php` if user is not authenticated.
- **User Data:** Loads user info (`name`, `email`) from session.

### 2. Localization & Language Support
- **Arabic Language:** UI strings and site direction are set for Arabic. Language array (`$lang`) holds keys for translation.
- **RTL layout:** Document direction set based on selected language.

### 3. Includes & Dependencies
- **Initialization:** `includes/init.php`
- **Icons Helper:** `includes/icons.php` for retrieving icon classes.
- **Notifications Logic:** `includes/notification_functions.php` (fetch, update, delete notifications/settings).

### 4. Notification Data Handling
- **Fetch Notifications:** Retrieves up to 50 most recent notifications for the user.
- **Notification Settings:** Loads user-specific settings (channels and types); creates default settings if not present.
- **Time Formatting:** Custom `timeAgo` function to present dates in relative (e.g., "قبل دقيقة") or absolute format.

### 5. User Actions & Processing

#### A. Notification Management
- **Mark All as Read:** User can mark all notifications as read with one click.
- **Mark Single as Read:** User can mark individual notifications as read.
- **Delete Notification:** User can delete individual notifications after confirmation dialog.

#### B. Adjust Notification Settings
- **Channels:** Enable/disable notification via email or SMS.
- **Types:** Toggle specific notification types (upcoming tickets, event changes, transport updates, payment, admin announcements).
- **Settings Update:** Form submission updates settings in database.

### 6. UI Structure

#### A. Header
- **Logo and Site Name**
- **Navigation menu:** Home, Events, About, Contact.
- **User Dropdown:** Access account pages, including notifications, tickets, logout.

#### B. Main Content
- **Sidebar:** Compact user profile view and navigation to account sections.
- **Notifications List:** 
  - Displays each notification with appropriate icon and "new" highlighting.
  - Actions next to each notification (mark as read, delete).
  - Shows friendly message if there are no notifications.
- **Settings Form:** Allows flexible opt-in/out of various notification methods and types using toggle switches.

#### C. Footer
- Static footer with copyright.

### 7. Styling
- **Tailwind CSS:** For rapid, responsive, modern styling.
- **FontAwesome:** For clear and context-aware icons.
- **Custom AR/EN direction and spacing classes** for proper layout in RTL or LTR modes.

### 8. Miscellaneous
- **Error/Success Messaging:** All result of user actions are messaged back at the top of the main content.
- **Accessibility:** Button and label usage, alert regions, and visually clear toggle switches.

---

## Security Considerations
- Restricts notification operations to authenticated users based on session.
- Sanitizes input (validates notification IDs as numeric).

---

## Extensibility and Maintenance
- **Language support** is centralized in `$lang`, making it easy to extend for multi-language UIs.
- **Modular includes** allow for easy updates to icons, notification functions, and site-wide initialization.
- **Structured CSS and markup** facilitate UI customization.

---

## Target Audience
- **End-users** of the ticketing platform needing to manage their account notifications and preferences.
- **Developers** wishing to customize or extend the notification system.

---

**Summary:**  
This is a user-centric notifications management system with real-time actions, customizable settings, and modern presentation, built with PHP and Tailwind, designed for Arabic-speaking users of "تذاكر فلسطين".