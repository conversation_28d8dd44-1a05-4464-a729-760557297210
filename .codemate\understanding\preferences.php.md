# High-Level Documentation: Account Preferences Page

## Overview
This PHP file provides the **Account Preferences** page for a user dashboard in a web application. It enables authenticated users to:

- View and update their preferred language.
- Select and update their preferred timezone from a curated list.
- Navigate to other account management pages via a sidebar.
- See confirmation or error messages when updating preferences.

The page is styled with Tailwind CSS and includes support for multiple languages (localization).

---

## Major Functional Components

### 1. **User Authentication & Session Management**
- Uses an `Auth` class to check if the user is logged in.
- If not authenticated, the user is redirected to the login page.

### 2. **Data Retrieval**
- Retrieves current user data from the database based on the session's `user_id` to prefill preference fields and for display in the sidebar.

### 3. **Preferences Update Logic**
- Detects POST requests to update preferences (`preferred_language` and `timezone`).
- Updates the database with the new values.
- Updates the session language.
- Reloads user data and language file as appropriate.
- Displays success or error messages based on the update result.

### 4. **Sidebar Navigation**
- Provides links to:
    - View/edit user profile
    - My tickets
    - Payment methods
    - Invoices
    - Notifications
    - **Account Preferences** (current active page)
    - Security
    - Logout
- Displays user name, email, and profile image (or fallback to first letter of name).

### 5. **Main Content Area: Account Preferences Form**
- Form lets users:
    - Select their **preferred language** (Arabic, English, Hebrew).
    - Choose a **timezone** from the Middle East, North America, South America, and Europe.
- Displays contextual notes for language and timezone preferences.
- Submits updates to self.
- Buttons and messages localized via the `$lang` array.

### 6. **Localization**
- Supports multiple languages for UI and alert messages.
- Loads the appropriate language file based on user or session preference.

### 7. **Admin Role Detection**
- Checks user role to identify if the user is an admin; can facilitate additional admin options elsewhere (not utilized directly in this file).

---

## Security and Best Practices

- **Session Handling**: All actions are scoped to the logged-in user's session.
- **Prepared Statements**: User inputs are bound securely to prevent SQL injection.
- **Role Checking**: Basic detection for `admin` users, possibly for RBAC features.

---

## Extensibility

- **Modularization**: Uses reusable includes for initialization, authentication, functions, and headers/footers.
- **Icon Management**: Sidebar icons are handled by a helper function (`get_icon()`), making UI theme updates manageable.
- **Timezone and Language Options**: Can easily be expanded by editing the `$timezones` list and language selection dropdown.

---

## Typical Workflow

1. User logs in and reaches this page.
2. The form is prefilled with the user's current preferences from the database.
3. User selects new language or timezone and submits form.
4. The server processes the change, updates the database and session, refreshes the page with a confirmation or error message.
5. Sidebar allows easy navigation to other user account sections.

---

## Footer

- Standard footer is included at the bottom of the page using the shared include.

---

**End of Documentation**