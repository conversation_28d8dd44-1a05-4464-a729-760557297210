**High-Level Documentation**

---

### Overview

This script handles the AJAX-based update of discount coupon information within an administrative panel of a web application. Only authenticated administrators can use this endpoint. It performs data validation, updates the database record for the coupon, and returns a JSON response with success/error messages.

---

### Key Functional Steps

1. **Session and Dependencies**
   - Ensures a user session exists.
   - Loads required configurations and utility functions.
   - Establishes a database connection.

2. **Authentication & Security**
   - Requires the user to be an authenticated admin.
   - Applies CSRF (Cross-Site Request Forgery) token validation to protect against unauthorized form submissions.

3. **Input Handling & Validation**
   - Accepts POST data with fields for coupon ID, code, type, value, usage limit, expiration date, and CSRF token.
   - Sanitizes input to prevent basic injection/attack vectors.
   - Validates business rules such as:
     - Required fields (`code`, `type`).
     - Value correctness (`value` > 0, percentage ≤ 100).
     - Coupon code uniqueness.

4. **Database Operation**
   - Updates the corresponding coupon record in the database if all validations pass.
   - Fetches the updated record for confirmation.

5. **Internationalization**
   - Supports returning localized success messages based on the current session language setting.

6. **Error Handling & Response**
   - Handles and logs database errors, returning appropriate error messages to the frontend.
   - Returns all responses as JSON, indicating success/failure as well as error or success messages, and updated coupon info if applicable.

---

### Typical Use Case

A frontend admin interface sends an AJAX `POST` request to this script with updated coupon information. The script validates and processes the request, then returns a confirmation or error for the frontend to display.