# Security Vulnerability Report

## PHP Code Security Analysis

This report focuses exclusively on security vulnerabilities in the provided PHP code sample. The assessment includes analysis of data output to the front-end, risks of code/design, and common attack vectors in web applications (e.g., XSS, SQL Injection, CSRF, etc.).

---

## 1. **Cross-Site Scripting (XSS) Vulnerabilities**

### A. **Unsafe Output of Unescaped Data**

#### Language Array and Event Properties
Throughout the code, numerous values are output directly into the HTML without proper escaping. For example:

```php
<h1 class="..."><?php echo $lang['hero_title'] ?? 'عنوان البطل'; ?></h1>
...
<img src="<?php echo !empty($event['image']) ? $event['image'] : 'assets/img/event-placeholder.jpg'; ?>" ... />
...
<h5 class="..."><?php echo $event['title']; ?></h5>
...
<p class="text-gray-600 mb-3 flex-1"><?php echo mb_substr($event['description'], 0, 100); ?>...</p>
...
<span><?php echo $event['price']; ?> ₪</span>
...
<a href="event-details.php?id=<?php echo $event['id']; ?>" ...>تفاصيل الفعالية</a>
```

#### Issue:
**No Output Escaping:**  
Directly echoing data from arrays such as `$lang` and `$event` exposes the application to Cross-Site Scripting (XSS) attacks if those values can be user-controlled or injected (either directly or indirectly).

**Risk Examples:**
- If event titles/descriptions, images, or language strings are stored in a database and can be set by untrusted users, an attacker could inject malicious JavaScript.
- The `img src` and `alt` attributes, `a href`s, and other content are particularly susceptible.
  
#### Mitigation:
- **Always escape output:** Use `htmlspecialchars($string, ENT_QUOTES, 'UTF-8')` for any data rendered into HTML.
    - For HTML attribute context: `htmlspecialchars` on attribute values.
    - For URLs (like `href` or `src`): use `urlencode()` for parameter parts or validate/whitelist expected sources.
- Only use raw PHP echo on static, trusted content.
- Review any user-generated content or database-stored fields that could be manipulated.

**Example:**
```php
<h5 class="..."><?php echo htmlspecialchars($event['title'], ENT_QUOTES, 'UTF-8'); ?></h5>
```

### B. **HTML Attributes Vulnerable to Injection**

```php
<img src="<?php echo !empty($event['image']) ? $event['image'] : 'assets/img/event-placeholder.jpg'; ?>" ... alt="<?php echo $event['title']; ?>">
```

#### Issue:
Unescaped image sources and alt text could allow breaking out of the attribute and injecting scripts.

#### Mitigation:
- Ensure the `src` value is both validated (matches e.g., images directory, no external URLs unless trusted) and escaped.
- Use `htmlspecialchars()` for `alt` attributes.

---

## 2. **IDOR (Insecure Direct Object Reference) / URL Parameter Tampering**

```php
<a href="event-details.php?id=<?php echo $event['id']; ?>" ...>تفاصيل الفعالية</a>
```

#### Issue:
If `event-details.php` does not properly validate that a user is authorized to see a given event's details (based on the id param), or has no checks for id manipulation, attackers may access or manipulate data they should not.

#### Mitigation:
- Validate and sanitize all incoming parameters server-side.
- Check access control on `event-details.php` for all actions based on `id`.

---

## 3. **Missing CSRF Protection**

### Newsletter Subscription Form

```html
<form class="flex">
    <input type="email" ... required>
    <button type="submit" ...>اشترك</button>
</form>
```

#### Issue:
If this form makes any changes on the back-end or triggers actions (e.g., adding an email), it is susceptible to Cross-Site Request Forgery (CSRF) without a CSRF token.

#### Mitigation:
- Implement CSRF tokens in all forms submitting data.
- Verify the token server-side upon processing the submission.

---

## 4. **Lack of Input Validation/Sanitization**

- There is no visible validation on parameters such as event `id` or data returned from functions (e.g., `get_events()`).
- `get_events(6)` function is assumed safe; risks remain if unsanitized database output is rendered.

#### Issue:
Any unsanitized user input, or data passed through functions that interact with the database, can lead to SQL Injection (if used in queries), XSS, or logic bugs.

#### Mitigation:
- Use prepared statements and parameterized queries in all DB calls.
- Sanitize and validate all inputs (even those from internal functions).

---

## 5. **Error Reporting in Production**

```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```

#### Issue:
- In production, displaying errors reveals internal paths, queries, configuration, and code structure to users, which is valuable to attackers (information disclosure).
- **Exception messages** are echoed to the end user in the catch block:
```php
die("خطأ في تحميل الملفات: " . $e->getMessage());
```
This may reveal sensitive server info.

#### Mitigation:
- NEVER set `display_errors` to `1` in production; always log errors instead.
- Catch exceptions logs for administrative review, and display only generic user-friendly error messages.

---

## 6. **Potential SQL Injection**

While not directly visible in the code provided, if the helper function `get_events()` or any data loading mechanism does not use prepared statements, unsanitized user input could lead to SQL Injection.

#### Mitigation:
- Ensure **all database queries** use parameterized queries or a safe ORM.
- Never interpolate user input directly into SQL.

---

## 7. **Miscellaneous / Best Practices**

- **File Inclusion:** Ensure `require_once 'includes/init.php';` and other includes do not use user input in paths, to avoid file inclusion attacks.
- **HTTP Security Headers:** Not related to this file directly, but ensure that outputting content is accompanied by appropriate headers (Content-Security-Policy, X-Frame-Options, etc.)
- **Session Security:** Not present in this code; ensure all authentication and session management is done securely across the application.

---

# Summary Table

| Vulnerability                | Severity | Risk Description                         | Remediation                                   |
|------------------------------|----------|------------------------------------------|-----------------------------------------------|
| XSS via Unescaped Output     | High     | User input is directly rendered to HTML  | Always escape all output with `htmlspecialchars()` |
| IDOR via URL param           | Med      | Direct access to objects via ID in URL   | Validate/authorize all resource accesses      |
| Missing CSRF on forms        | Med      | Forms vulnerable to CSRF                 | Add CSRF tokens to all forms                  |
| Error Info Disclosure        | Med      | Application details leaked on error      | Do not display errors in production           |
| Input Validation             | High     | Unsanitized data sent to front-end/db    | Validate and sanitize all inputs              |
| SQL Injection risk           | High     | If `get_events()` is vulnerable          | Always use prepared statements                |

---

# **Recommendations**

- **Review all echo statements** and ensure appropriate encoding/escaping.
- **Whitelist and validate** data loaded from the DB and user-controlled fields.
- **Sanitize/escape all attributes, URLs, and outputs** especially in places where user data from `$event`, `$lang`, or forms are used.
- **Add CSRF protection** for forms performing state-changing actions.
- **Hide all errors from users in production**, log them instead.
- **Audit all DB interactions** for SQL Injection vulnerabilities.
- **Deploy HTTP security headers** and other platform-wide best practices.

---