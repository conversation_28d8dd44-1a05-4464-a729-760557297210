# High-Level Documentation: Admin Contact Messages Management Page

## Overview

This PHP script implements the admin interface for managing "Contact Us" messages submitted by users through a website. Key features include:

- **Login-required admin access (with redirect)**
- **Paginated, tabular listing of contact messages**
- **Message actions: View, Mark as Read, Delete**
- **Unread messages highlight and badge**
- **Modal dialog for message details and quick actions**
- **Bootstrap-based UI with dynamic behavior via JavaScript**

---

## Major Components and Flow

### 1. **Authentication & Access Control**
- Ensures only logged-in admin users can access the page.
- If the user is not authenticated as admin, they are redirected to the login page.
- Stores intended destination to allow redirect after successful login.

### 2. **Includes & Setup**
- Loads utility functions and authentication helpers.
- Sets the page title and includes the admin header template.

### 3. **Action Handling**
- Recognizes two actions via query parameters:
    - `mark_read`: Marks a specific message as read in the database.
    - `delete`: Deletes a specific message from the database.
- Sets corresponding success/error messages in the session and redirects after actions.

### 4. **Message Retrieval with Pagination**
- Fetches total message count and calculates pagination info.
- Retrieves a limit of 10 messages per page, ordered by most recent.
- Also retrieves the count of unread messages for badge display.

### 5. **Rendering the Messages Table**
- Renders a Bootstrap table of messages with the following columns:
    - Name, Email, Subject, Date, Status, Actions
- Highlights unread messages visually.
- Provides action buttons on each row:
    - View (opens modal dialog with message)
    - Mark as Read (if unread)
    - Delete (confirmation required)

### 6. **Pagination Controls**
- Renders pagination controls if there is more than one page of messages.

### 7. **Message Modal Dialog**
- Modal displays message details when a message's "view" button is clicked.
- Provides modal buttons for:
    - Mark as Read (if not already read)
    - Reply by Email (mailto link)
    - Delete (with confirmation)
    - Close

### 8. **JavaScript Enhancements**
- Handles populating and displaying the message modal when viewing a message.
- If viewing an unread message, automatically marks it as read via a `fetch` GET request.
- Forces a page reload after the modal closes (to update the unread count/badge).

### 9. **Footer Inclusion**
- At the end, includes the admin page footer.

---

## Security Considerations

- All actions affecting message state (`mark_read`, `delete`) are protected via admin authentication.
- Outputs user-generated content (name, email, subject, message) through `htmlspecialchars` to prevent XSS.
- Deletion requires user confirmation.

---

## Technologies Used

- **PHP (procedural style, session-based auth)**
- **PDO/DB class for database access (implicitly, via `$db`)**
- **Bootstrap (for modals, tables, badges)**
- **Vanilla JavaScript for modal behavior**

---

## Extensibility

The structure allows for further enhancements, such as:
- Adding filtering or searching for messages
- Logging actions taken by admins
- Implementing AJAX for all mark-as-read and delete operations without page reload

---

## File Dependencies

- `includes/functions.php`, `includes/auth_functions.php` – utility/auth code
- `includes/admin_header.php`, `includes/admin_footer.php` – layout templates

---

## Summary

This script provides a secure, paginated, admin-only interface for reading and managing "Contact Us" messages, complete with modal previews and message state management. It combines server-side PHP validation and management with a responsive, interactive client-side experience.