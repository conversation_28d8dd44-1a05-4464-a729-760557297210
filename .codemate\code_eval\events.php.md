# Critical Code Review Report

## General Review Points

### 1. Error Reporting in Production

**Issue:**  
`error_reporting(E_ALL);` and `ini_set('display_errors', 1);` should **never be enabled in production**. This may expose sensitive data.

**Correction:**  
```php
// Only enable error reporting during development, not in production!
if ($_SERVER['APP_ENV'] === 'development') { 
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
```

---

### 2. Output Encoding (XSS Risk)

**Issue:**  
Directly outputting content from `$event['title']`, `$event['description']`, `$event['location']`, and other variables **without output encoding** opens the system to Cross-site scripting (XSS).

**Correction:**  
Replace all occurrences like
```php
<?php echo $event['title']; ?>
```
with:
```php
<?php echo htmlspecialchars($event['title'], ENT_QUOTES, 'UTF-8'); ?>
```
(similar for `description`, `location`, etc.)

---

### 3. Undefined Variables

**Issue:**  
`$lang` and `$selected_lang` are used but there’s no check if these are defined/initialized.

**Correction:**  
Add before their usage:
```php
if (!isset($lang)) { $lang = []; }
if (!isset($selected_lang)) { $selected_lang = 'en'; }
```

---

### 4. Event Data Existence

**Issue:**  
If `get_events()` returns false/null, the `foreach($events as $event)` will fail.

**Correction:**  
Before foreach loop, check for valid array:
```php
if (!is_array($events)) { $events = []; }
```
Or safer:
```php
<?php if (!empty($events) && is_array($events)): ?>
    <?php foreach($events as $event): ?>
        // existing code
    <?php endforeach; ?>
<?php else: ?>
    <div class="text-center py-8 text-gray-600"><?php echo $lang['no_events'] ?? 'لا توجد فعاليات حالياً.'; ?></div>
<?php endif; ?>
```

---

### 5. Hardcoded Asset Paths

**Issue:**  
`'assets/img/event-placeholder.jpg'` is hardcoded and may break if moved. Use a helper constant if available.

**Correction:**  
```php
<img src="<?php echo !empty($event['image']) ? htmlspecialchars($event['image'], ENT_QUOTES, 'UTF-8') : ASSET_PATH . 'img/event-placeholder.jpg'; ?>" ... >
```
Where `ASSET_PATH` should be a defined constant.

---

### 6. SQL Injection (if event id used in SQL)

**Issue:**  
Though not in this snippet, if `event-details.php?id=...` is used without sanitization/parameterized query, it’s a risk.

**Suggestion:**  
In `event-details.php`, always filter/sanitize input:
```php
$id = isset($_GET['id']) ? (int) $_GET['id'] : 0;
```
Or use a whitelist with prepared statements.

---

### 7. Fallback for Multibyte Substrings

**Issue:**  
`mb_substr($event['description'], 0, 100);` may output an incomplete word/character and always appends `...`.

**Suggestion:**  
Trim on word boundaries.
```php
$desc = mb_strimwidth($event['description'], 0, 100, '...');
echo htmlspecialchars($desc, ENT_QUOTES, 'UTF-8');
```

---

### 8. Redundant/Unoptimized PHP

**Example:**
When checking for language, the check is done many times per iteration with `($selected_lang == 'en') ? ... : ...;`.  
Cache the result before the loop.

**Correction:**  
Before the loop:
```php
$is_en = ($selected_lang === 'en');
```
And use `$is_en` instead of repeating the ternary.

---

### 9. Accessibility

**Issue:**  
`<img ... alt="<?php echo $event['title']; ?>">` is good, but better is:
```php
alt="<?php echo htmlspecialchars($event['title'], ENT_QUOTES, 'UTF-8'); ?>"
```
Also, for color-contrast, run CSS checks.

---

### 10. Use of `require_once` for footer

**Note:**  
Generally, `include` is preferred for layout elements so that if the file is missing, the page still loads.  
But if your application REQUIRES the footer, this is fine.

---

## Summary

### Key Changes:

- **Error reporting should be environment-dependent.**
- **All dynamic HTML outputs must be run through `htmlspecialchars`.**
- **Check for variable existence (`$lang`, `$selected_lang`, `$events`).**
- **Process event description more thoughtfully (word-break, etc.).**
- **Reduce code repetition (like repeated `$selected_lang == 'en'`).**
- **Sanitize all user input/URL parameters, especially IDs.**
- **Use configuration/constants for paths, do not hardcode.**

---

## Example Pseudocode Corrections

```php
// Secure dynamic display & environment handling
if (!isset($lang)) { $lang = []; }
if (!isset($selected_lang)) { $selected_lang = 'en'; }
$is_en = ($selected_lang === 'en');
if (!is_array($events)) { $events = []; }

<?php if (!empty($events)): ?>
    <?php foreach($events as $event): ?>
        <h5><?php echo htmlspecialchars($event['title'], ENT_QUOTES, 'UTF-8'); ?></h5>
        <p><?php echo htmlspecialchars(mb_strimwidth($event['description'], 0, 100, '...'), ENT_QUOTES, 'UTF-8'); ?></p>
        ...
    <?php endforeach; ?>
<?php else: ?>
    <div>No events currently.</div>
<?php endif; ?>
```

---

# Conclusion

By following the above corrections, the code will be much safer, more maintainable, and better aligned with industry standards. **Never echo user or database content unescaped**, and always structure code to defend against missing/invalid data and configuration changes.