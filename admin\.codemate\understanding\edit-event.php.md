# High-Level Documentation: Edit Event Page (Admin Panel)

## Overview
This PHP script provides the backend and frontend necessary for administrators to edit details of an event in a web application's admin panel. It connects to a database to fetch event information based on a given event ID and presents a form for editing the event. Form submissions, including file uploads, are handled asynchronously (AJAX) for a smoother user experience.

---

## Workflow Summary

1. **Access Control & Event Validation**
   - Ensures an `id` is provided via the query string (GET parameter).
   - Redirects to the event listing page if the `id` is missing or invalid.
   - Requires the user to have admin privileges (via `requireAdmin()`).

2. **Data Retrieval**
   - Fetches event details from the database using the provided ID.
   - Redirects if the event does not exist.

3. **Form Preparation**
   - Populates the form with current event details, including title, description, date, time, location, prices, category, available tickets, whether featured, and existing event image.
   - Generates a CSRF token for security.

4. **UI Elements**
   - Displays the form in an organized layout with grouped fields for general info, pricing, availability, and image upload.
   - If the event has an image, shows a thumbnail preview.
   - Provides an image preview when a new image file is selected before submission.

5. **Form Submission (AJAX)**
   - Uses JavaScript to submit the form asynchronously to a separate handler (`update_event_ajax.php`) with all form data, including any image.
   - Displays a loading spinner and provides feedback after success or failure.
   - Redirects to the events listing on successful update.

6. **Security**
   - Enforces admin-only access.
   - Incorporates CSRF protection.
   - Uses output escaping (htmlspecialchars) for form values to prevent XSS.

---

## Components

### Backend (PHP)
- **Authentication**: Calls `requireAdmin()` to restrict page access to admins.
- **Localization**: Uses `$lang` for all labels (assumes a multi-language solution).
- **Database**: Fetches event from the `events` table using a prepared statement.
- **Session Handling**: Sets error messages for missing data or unauthorized access.
- **Page Structure**: Separates page content with includes for standardized headers and footers.

### Frontend (HTML & JS)
- **Form Fields**: Provides inputs for all essential event attributes, including file input for image updates.
- **Image Preview**: Uses FileReader API to preview a newly selected image before upload.
- **AJAX Submission**: Submits form asynchronously with fetch API, handles responses, and displays status messages accordingly.
- **Redirection**: Redirects back to events list upon successful update.

---

## Notable Features

- **User Experience**: AJAX prevents full page reload and provides immediate feedback.
- **Flexible Pricing**: Supports both original and discounted price, with explanatory tooltips for administrators.
- **Security Considerations**: Protects against unauthorized access and request forgery.
- **Extensibility**: The code is structured for easy theme/language changes and future field additions.

---

## Expected Usage

This file is to be used by administrators who wish to edit existing events, ensuring all data modifications, including uploading/changing event images, are validated, user-friendly, and secure.

---

**Note:** The actual update logic (database manipulation, file handling, extra validations) is handled separately in `update_event_ajax.php`.