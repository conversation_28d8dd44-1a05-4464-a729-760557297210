# PHP Code Review Report

## General Code Review

Below is a critical review focusing on industry standards (readability, security, maintainability), optimization, error handling, and best practices. **Any required corrected pseudo code is provided following each item.**

---

### 1. **Error Reporting in Production**

**Issue:**   
You are enabling error display globally. This should never be done in production as it leaks sensitive information.

**Correction Pseudo Code:**
```php
if (ENVIRONMENT !== 'production') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}
```

---

### 2. **CSRF Protection**

**Issue:**   
There's no Cross-Site Request Forgery protection on the form, which is essential for login forms.

**Correction Pseudo Code:**
```php
// When rendering the form:
<input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">

// When handling POST:
if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
    $error = 'غير مصرح بالطلب.';
    // Stop further processing.
}
```

---

### 3. **Input Validation and Sanitization**

**Issue:**   
Direct use of `$_POST['email']` and `$_POST['password']` without any validation or sanitization.

**Correction Pseudo Code:**
```php
$email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
$password = $_POST['password'] ?? '';

if (!$email || empty($password)) {
    $error = 'الرجاء إدخال بريد إلكتروني صالح وكلمة مرور.';
}
```

---

### 4. **Session Hijacking Risk**

**Issue:**   
No session regeneration upon successful login, increasing risk for session fixation.

**Correction Pseudo Code:**
```php
if($auth->login($email, $password)) {
    session_regenerate_id(true);
    // ... rest of the login redirect
}
```

---

### 5. **Potential Undefined Variable (`$selected_lang`)**

**Issue:**   
`$selected_lang` is used without any guarantee of being set, which can cause notices.

**Correction Pseudo Code:**
```php
// Before using $selected_lang
$selected_lang = $selected_lang ?? 'ar'; // Safely default to a language
```

---

### 6. **XSS Vulnerability on Echo of User-Controllable or Dynamic Values**

**Issue:**   
All output should be properly escaped. In several places (e.g., `echo $lang[...]` and `echo $error`), values could be user-controllable.

**Correction Pseudo Code:**
```php
// Use htmlspecialchars for all output
echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8');
echo htmlspecialchars($lang['...'], ENT_QUOTES, 'UTF-8');
```

---

### 7. **Require vs. Include and File Existence**

**Issue:**   
If any required file (e.g., `header.php`, etc.) is missing, a fatal error occurs. You’re catching with try/catch but `require_once` does not throw Exception— it emits a warning/fatal error. Try/catch here is misleading.

**Correction Pseudo Code:**
```php
foreach (['init.php', 'functions.php', 'header.php'] as $file) {
    $path = "includes/$file";
    if (!file_exists($path)) {
        die("خطأ في تحميل الملف: $file");
    }
    require_once $path;
}
```

---

### 8. **Unsecured Forgot Password Link**

**Issue:**   
The "forgot password" link currently links to `#` (does nothing). This can be misleading or bad UX.

**Correction Pseudo Code:**
```php
<a href="forgot_password.php" ...>
```

---

### 9. **Accessibility and Usability**

**Issue:**   
Input fields lack value preservation on failed login (user must re-type email). Password field is okay for security.

**Correction Pseudo Code:**
```php
<input type="email" ... value="<?php echo htmlspecialchars($_POST['email'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
```

---

### 10. **Form Action**

**Issue:**   
The form has no explicit `action` attribute. While default is current file, being explicit is best practice.

**Correction Pseudo Code:**
```php
<form method="post" action="">
```

---

### 11. **Footer Include Should Use `require_once` or `include_once`?**

**Observation:**   
Using `require_once 'includes/footer.php';` is fine unless it could be missing, in which case a more graceful handling may be desired.

---

### 12. **Password Security (Recommendation)**

**Note:**  
Make sure in your `Auth::login()` implementation you correctly hash passwords—never store plaintext or unsalted hashes.

---

## **Summary Table**

| Issue             | Found | Risk         | Suggested Correction                        |
| ----------------- | ----- | ----------- | ------------------------------------------- |
| Error Display     | Yes   | High        | Use ENV-based settings                      |
| CSRF              | No    | High        | Add CSRF tokens and verification            |
| Input Validation  | No    | High        | Validate/sanitize `$_POST` values           |
| Session Security  | No    | Medium      | Call `session_regenerate_id` after login    |
| Undefined Vars    | Yes   | Medium      | Always initialize `$selected_lang`          |
| XSS Protection    | No    | High        | Use `htmlspecialchars` on all outputs       |
| File Includes     | Risk  | Medium      | Check file exists before require            |
| Forgot Password   | Issue | Low         | Link to real password recovery page         |
| Form UX           | No    | Low         | Retain email value on error                 |
| Form Action       | No    | Low         | Add explicit `action` attribute             |

---

## **Conclusion**

Applying the above best practices will reduce security risks (XSS, session fixation, CSRF), subtle runtime errors, and improve maintainability and user experience of your PHP login code.

---