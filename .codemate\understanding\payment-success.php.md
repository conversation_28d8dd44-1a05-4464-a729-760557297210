# High-Level Documentation: Payment Success Page

## Purpose

This PHP script serves as the "Payment Success" page after a user completes an event ticket purchase. It verifies the order, fetches relevant details, and displays a confirmation with booking information.

## Overview

- **Authentication:** Ensures the user is logged in; redirects to login if not.
- **Order Validation:** Checks if an order ID is provided. If not, redirects to the events page.
- **Order Retrieval:** Attempts to fetch order details:
  - If found, validates ownership.
  - If not, searches for a ticket purchase by the user as a fallback.
  - If nothing is found, redirects to the events page.
- **Event Retrieval:** Fetches event information linked to the order.
- **Total Calculation:** Recalculates the total amount for the order if it's missing.
- **View Rendering:**
  - Displays a confirmation message and booking summary.
  - Shows event details: name, date, location, number of tickets, order total.
  - Provides links to view all tickets or browse other events.
  - Multi-language support for messages and labels.
  - Responsive and styled with utility classes.

## Structure

1. **Includes & Initialization:** Loads helper files for initialization, functions, authentication, and header.
2. **Order & Ticket Logic:**
   - Validates session and order ID.
   - Pulls order info or constructs it from the ticket table if needed.
3. **Data Preparation:**
   - Fetches event details.
   - Updates order totals if necessary.
4. **User Interface:**
   - Shows a success icon and thank-you message.
   - Presents details in a styled summary box.
   - Informs user that ticket details will be emailed.
   - Navigation buttons to tickets or event listings.

## Key Features

- **Security:** Only shows order details to the purchasing user.
- **Fallback Handling:** If a standard order record does not exist, attempts to build the summary from ticket data.
- **i18n:** Uses language packs for labels (with Arabic defaults).
- **User Experience:** Gives clear next-action choices and all relevant booking information.

---

**Suitable for:** Event ticketing systems, confirming successful event bookings and summarizing transaction details to the user.