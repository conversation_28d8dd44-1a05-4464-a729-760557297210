# High-Level Documentation: Password Reset Script

## Purpose

This PHP script provides the backend and frontend logic for securely resetting a user's password as part of an authentication system. It is designed to be accessed via a special reset token sent to the user's email after requesting a password reset.

---

## Main Components

### 1. **Initialization & Includes**
- Loads required files needed for initialization, helper functions, authentication, and the page header.

### 2. **Authentication Check**
- If the user is already logged in, they are redirected to the homepage (`index.php`). Only unauthenticated users can reset a password via this page.

### 3. **Token Validation**
- Checks for a `token` parameter in the URL (GET request).
- Verifies that the token exists in the database for a user and has not expired.
- If valid, allows the user to proceed with the password reset process.
- If invalid or missing, displays appropriate error messages and offers a link to request a new reset token.

### 4. **Password Reset Form Handling**
- Accepts a new password and password confirmation via POST request.
- Validates that:
  - The new password meets minimum length requirements (6 characters).
  - Password and confirmation match.
- On successful validation:
  - Hashes the password securely using bcrypt.
  - Updates the user's password in the database.
  - Clears the reset token and expiration from the user's record.
  - Notifies the user of success and provides a login link.
- On failure, displays relevant error messages.

### 5. **User Interface**
- Responsive, styled HTML form (Tailwind CSS classes suggested).
- Displays relevant messages (success, error, instructions) in both Arabic and English, depending on language settings.
- Visual elements include icons and enhanced form usability.
- Always provides navigation back to the login page.

### 6. **Footer**
- Includes a footer at the end of the page.

---

## Security & Best Practices

- Only allows password reset via a valid, non-expired token.
- Passwords are hashed with bcrypt before database storage.
- Reset token is invalidated upon successful password reset.
- Avoids revealing specific reasons for token invalidity (prevents enumeration).
- Protects against unauthorized access during logged-in sessions.

---

## Localization

- Uses the `$lang` array and `$selected_lang` variable for multi-language support, making the page adaptable between Arabic and English.

---

## Usage Scenario

1. **User requests password reset** and receives an email with a reset link.
2. **User accesses reset link**, and the token is validated.
3. **User enters a new password and confirmation**, submitting the form.
4. **System validates and updates the password** if conditions are met.
5. **User receives success/error feedback** and is directed to login with the new password.

---

## Files Expected

- `init.php`, `functions.php`, `auth.php`, `header.php`, `footer.php` (for initialization and layout)
- Language array `$lang`
- `Database` and `Auth` classes

---

**In summary:**  
This script implements a secure, localized, and user-friendly password reset mechanism for a PHP web application, handling backend validation and frontend user experience for the password reset process.