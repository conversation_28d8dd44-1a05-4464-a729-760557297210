# CODE REVIEW REPORT

## File: `discounts.php`

This report analyzes your code for best industry practices, optimizations, and potential errors or vulnerabilities. Where necessary, improvements and corrected code snippets in **pseudo code** format are provided. All remarks are **critical** and written with industry standards in mind.

---

### 1. Uninitialized `$pdo` Variable

#### **Problem:**  
The code uses `$pdo` for database operations, but nowhere in the given code is `$pdo` initialized or included. This will cause an undefined variable error and is a security risk if error reporting is on in production.  

#### **Correction (pseudo code):**
```php
// Add (before any database operations):
require_once '../includes/db_connection.php'; // Ensure this sets up $pdo
```

---

### 2. Session Not Started

#### **Problem:**  
You are using `$_SESSION`, but do not call `session_start()` at the top. This will break session-dependent logic and can lead to "headers already sent" errors.

#### **Correction (pseudo code):**
```php
// Add at the very top (before any output):
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}
```

---

### 3. CSRF Token Should Be Per Form Instance

#### **Problem:**  
You generate a **single CSRF token** and reuse it for all delete forms, but modals are generated inside a loop. This opens the risk of replay attacks (one token for all delete actions).

#### **Correction (pseudo code):**
```php
// Inside the foreach loop for each discount, generate a token with the discount id as a context:
$csrf_token = generateCSRFToken('delete_discount_' . $discount['id']);

// In the form:
<input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

// On POST, verify with context:
if (!verifyCSRFToken($_POST['csrf_token'], 'delete_discount_' . $discountId)) { ... }
```

---

### 4. Superglobals Not Properly Escaped in Redirects

#### **Problem:**  
Direct redirection after setting a session message is fine, but it is best to sanitize all incoming data, especially if echoed or logged.  

#### **Correction:**  
In this code, you do properly cast the ID to int. However, as a best practice, **always sanitize and validate** ALL user inputs, including URL query parameters.

---

### 5. Possible Undefined Index in `$lang` array

#### **Problem:**  
You trust all `$lang[...];` keys are defined. If not, this will raise warnings.  

#### **Correction (pseudo code):**
```php
// Example usage in echo:
echo isset($lang['discount_deleted']) ? $lang['discount_deleted'] : 'Discount deleted';

// Optionally implement a helper:
function lang($key) {
    global $lang;
    return isset($lang[$key]) ? $lang[$key] : $key;
}
```

---

### 6. SQL Query: Fake `usage_count` Column

#### **Problem:**  
The query uses `0 as usage_count`. This can be misleading and is not correct in a real database-driven system. If usage count is required, either calculate via a related table or remove the column if not supported yet.

#### **Correction (pseudo code):**
```php
// Correct implementation would require a join/subquery:
SELECT c.*, 
       (SELECT COUNT(*) FROM coupon_usages u WHERE u.coupon_id = c.id) AS usage_count, 
       c.expiry_date AS expiration_date
FROM coupons c
ORDER BY c.created_at DESC
```

---

### 7. HTML: Table Column Count Issues

#### **Problem:**  
The column count in `<td colspan="8"` does not match your table header columns (which is 7). That will break layout/appearance.

#### **Correction (pseudo code):**
```php
<td colspan="7" class="text-center">
```

---

### 8. Comment on `is_active` Column

#### **Problem:**  
You mention `is_active` in comments but the column is not present. This is misleading and may confuse future maintainers.

#### **Correction (pseudo code):**
```php
// Remove comment: <!-- is_active column is not in the coupons table -->
```

---

### 9. Unnecessary try-catch on SELECT

#### **Observation:**  
While catching exceptions is good, handling a SELECT failure should log the error and perhaps display an admin-facing gentle message, not silently fall through with an empty array.

#### **Best Practice (pseudo code):**
```php
catch (PDOException $e) {
    error_log(...);
    $_SESSION['error_message'] = 'Database error occurred';
    header('Location: discounts.php');
    exit;
}
```

---

### 10. XSS Protection

#### **Observation:**  
You properly use `htmlspecialchars()` for discount codes (good!). Ensure this for **ALL** user data rendered, e.g., in table, buttons, and modals.

#### **Correction (pseudo code):**
```php
// For all data outputs, wrap with htmlspecialchars():
<?php echo htmlspecialchars($discount['value']); ?>
<?php echo htmlspecialchars($discount['id']); ?>
```

---

### 11. Function Existence Validation

#### **Problem:**  
You assume functions like `formatPrice`, `formatDate`, `generateCSRFToken`, and `verifyCSRFToken` exist and work as intended. No direct errors, but ensure their definitions exist, and that they are included everywhere used.

#### **Correction:**  
Check or add `require_once` for files that define these utility functions.

---

### 12. Modal IDs Must Be Unique

#### **Observation:**  
You use `deleteModal<?php echo $discount['id']; ?>`, which is correct and will generate unique IDs per item.

---

### 13. Security: Prepared Statements

#### **Observation:**  
You use prepared statements for deleting discounts, which is correct.

---

# SUMMARY OF CHANGES (PSEUDO CODE EXAMPLES)
Below are key corrections to be made in the source file:

```php
// At the very top:
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}
require_once '../includes/db_connection.php'; // Assumes this sets up $pdo

// In the foreach for discounts (for each modal/form)
$csrf_token = generateCSRFToken('delete_discount_' . $discount['id']);
<input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

// On POST processing/delete (when verifying token)
if (!verifyCSRFToken($_POST['csrf_token'], 'delete_discount_' . $discountId)) { ... }

// SQL query for usage_count:
SELECT c.*, 
       (SELECT COUNT(*) FROM coupon_usages u WHERE u.coupon_id = c.id) AS usage_count,
       c.expiry_date AS expiration_date
FROM coupons c
ORDER BY c.created_at DESC

// Table colspan correction:
<td colspan="7" class="text-center">

// Remove inaccurate/misleading comment:
<!-- is_active column is not in the coupons table --> // (REMOVE)
```

---

# FINAL RECOMMENDATIONS

- Always validate and sanitize **all** user inputs.
- Make sure all language strings exist, or create a helper function.
- CSRF tokens should be specific per form instance to avoid replay attacks.
- Ensure `$pdo` and all utility functions are always initialized/included.
- Double-check all user output for `htmlspecialchars()` wrapping.
- Remove stale/misleading code comments.
- If business logic changes, update SQL and PHP to reflect actual requirements (e.g. usage count).

---

**If you implement all the above corrections, your code will be more robust, maintainable, secure, and closer to industry best practices.**