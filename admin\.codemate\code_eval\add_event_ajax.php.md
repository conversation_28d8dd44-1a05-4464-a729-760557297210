# Code Review Report

## File: Event Creation API (PHP)

---

### 1. **Security & Input Validation**

#### Problems Detected:

- **Unsanitized File Name Before Upload:**  
  You directly use the original file name for image upload after simple concatenation. If a user provides a file name with malicious content or special characters, it could lead to security issues.

  **Correction:**  
  Use a whitelist approach and sanitize file names:

  ```php
  // Replace unsafe characters to prevent directory traversal and other attacks
  $safeFileName = preg_replace('/[^a-zA-Z0-9_\.-]/', '_', basename($_FILES['image']['name']));
  $fileName = time() . '_' . $safeFileName;
  $uploadFile = $uploadDir . $fileName;
  ```

- **No File Size Limit on Uploads:**  
  Large files could exhaust storage or system memory.

  **Correction:**  
  Set and validate a file size limit, for example 2MB:

  ```php
  $maxFileSize = 2 * 1024 * 1024; // 2MB
  if ($_FILES['image']['size'] > $maxFileSize) {
      $errors[] = 'Image size must be less than 2MB';
  }
  ```

- **MIME Type Check is Not Robust:**  
  A check using `getimagesize()` is helpful, but validating MIME-type directly is more robust.

  **Correction:**  
  Validate allowed extensions and MIME types:

  ```php
  $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!in_array(mime_content_type($_FILES['image']['tmp_name']), $allowedTypes)) {
      $errors[] = 'Invalid image type. Only JPG, PNG, GIF, and WEBP allowed.';
  }
  ```

#### [NEW CODE SUGGESTIONS]

```php
// After checking UPLOAD_ERR_OK
$maxFileSize = 2 * 1024 * 1024; // 2 MB
if ($_FILES['image']['size'] > $maxFileSize) {
    $errors[] = 'Image size must be less than 2MB';
}

$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array(mime_content_type($_FILES['image']['tmp_name']), $allowedTypes)) {
    $errors[] = 'Invalid image type. Only JPG, PNG, GIF, and WEBP allowed.';
}

// Sanitize filename
$safeFileName = preg_replace('/[^a-zA-Z0-9_\.-]/', '_', basename($_FILES['image']['name']));
$fileName = time() . '_' . $safeFileName;
$uploadFile = $uploadDir . $fileName;
```

### 2. **Business Logic Validation**

#### Problems Detected:

- **Date and Time Combination**:  
  No validation is done to ensure that `endDateTime` is after `dateTime`, which could allow invalid event intervals.

  **Correction:**  

  ```php
  if (strtotime($endDateTime) <= strtotime($dateTime)) {
      $errors[] = 'End time must be after start time';
  }
  ```

- **Original Price Less Than Price**:  
  If `original_price` is used for discounts/on sale logic, ensure it is not less than `price`.

  **Correction:**  

  ```php
  if ($originalPrice !== null && $originalPrice < $price) {
      $errors[] = 'Original price must be greater than or equal to the sale price.';
  }
  ```

#### [NEW CODE SUGGESTIONS]

```php
if (strtotime($endDateTime) <= strtotime($dateTime)) {
    $errors[] = 'End time must be after start time';
}

if ($originalPrice !== null && $originalPrice < $price) {
    $errors[] = 'Original price must be greater than or equal to the sale price.';
}
```

### 3. **Performance & Unnecessary Operations**

#### Problems Detected:

- **Querying Table Columns Every Time**  
  The `SHOW COLUMNS FROM events` query for every insert is unnecessary overhead and should be removed. Schema structure should be static and known by the application.

  **Correction:**  
  _Remove these lines entirely:_

  ```php
  // Get table structure for debugging
  $stmt = $pdo->query("SHOW COLUMNS FROM events");
  $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
  $columnNames = [];
  foreach ($columns as $column) {
      $columnNames[] = $column['Field'];
  }
  ```

### 4. **Error Handling**

#### Problems Detected:

- **Leaking SQL Error Messages to End Users**  
  Exposing `$e->getMessage()` to users is a security flaw. Only log errors server-side, and display a generic message.

  **Correction:**  

  ```php
  error_log("SQL Error: " . $e->getMessage());
  $response['message'] = 'A database error occurred. Please try again later.';
  ```

### 5. **Header & Response Handling**

#### Problems Detected:

- **Headers Might Be Sent After Output**  
  If any output precedes the `header()` function (e.g., PHP warnings/errors or whitespace before `<?php`), the `Content-Type` header may not be set correctly.

  **Correction:**  
  _Ensure strictly no output before calling `header()`. This is a best practice reminder._

---

## Summary Table

| Issue                                  | Correction/Best Practice              |
| --------------------------------------- | ------------------------------------- |
| Sanitize filenames for upload           | Use regex to replace unsafe chars     |
| Set maximum upload file size            | Validate `$_FILES['image']['size']`   |
| Validate image MIME type                | Use `mime_content_type` + allowlist   |
| Validate event date/time logic          | Check `endDateTime > dateTime`        |
| Enforce original_price >= price rule    | Add appropriate conditional           |
| Remove schema fetch on every insert     | Delete related code block             |
| Do not expose SQL errors to users       | Log errors, show generic message      |
| Ensure no output before `header()`      | (Observe best practice)               |

---

## **Actionable Insertions (Pseudo Code)**

```php
// FILE: Before moving uploaded file
$maxFileSize = 2 * 1024 * 1024;
if ($_FILES['image']['size'] > $maxFileSize) {
    $errors[] = 'Image size must be less than 2MB';
}

$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array(mime_content_type($_FILES['image']['tmp_name']), $allowedTypes)) {
    $errors[] = 'Invalid image type. Only JPG, PNG, GIF, and WEBP allowed.';
}

$safeFileName = preg_replace('/[^a-zA-Z0-9_\.-]/', '_', basename($_FILES['image']['name']));
$fileName = time() . '_' . $safeFileName;
$uploadFile = $uploadDir . $fileName;

// FILE: After combining $dateTime, $endDateTime
if (strtotime($endDateTime) <= strtotime($dateTime)) {
    $errors[] = 'End time must be after start time';
}

if ($originalPrice !== null && $originalPrice < $price) {
    $errors[] = 'Original price must be greater than or equal to the sale price.';
}

// FILE: In catch(PDOException ... )
error_log("SQL Error: " . $e->getMessage());
$response['message'] = 'A database error occurred. Please try again later.';

// FILE: Remove unnecessary schema columns query
// Remove code block fetching SHOW COLUMNS FROM events
```

---

**Apply the above changes to conform to industry best practices for secure, performant, and maintainable PHP server-side code.**