# 🔎 Code Review Report: Reset Password Page (PHP)

---

## 1. **Security Issues**

### 1.1. **Using Unsanitized Input (`$_GET['token']`)**

Raw use of `$_GET['token']` in the database query can be a risk, even with PDO binding, if not properly validated and type-checked (e.g., length).

**Suggestion:**
```pseudo
if (!preg_match('/^[a-f0-9]{64}$/i', $_GET['token'])) {
    $error = 'رمز إعادة التعيين غير صالح';
}
```
(Adjust `{64}` to actual token length.)

---

### 1.2. **Timing Attacks / Token Validity**

No timing-safe compare is used for tokens. If multiple tokens are possible (not shown), consider a timing-safe compare (less critical in DB context, but best practice).

---

### 1.3. **No Rate Limiting**

There is no rate limiting or brute force protection for password reset attempts.

**Suggestion:**  
Add logic:  
```pseudo
if (tooManyAttempts($_SERVER['REMOTE_ADDR'])) {
    $error = 'عدد المحاولات تجاوز الحد، يرجى الانتظار';
}
```
Implement `tooManyAttempts()` as rate limiting.

---

### 1.4. **No CSRF Protection on Form**

The form does **not** include a CSRF token.

**Suggestion:**  
Generate and verify CSRF token:
```pseudo
// In the form (inside <form>...</form>):
<input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
```
and in processing:
```pseudo
if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    $error = 'طلب غير صالح';
}
```
Generate the token in session on GET.

---

### 1.5. **Displaying Raw Error Messages**

Always sanitize output for HTML contexts to prevent XSS via PHP error/output.

**Suggestion:**
```pseudo
<p><?php echo htmlspecialchars($error); ?></p>
```

---

### 1.6. **No Password Complexity Requirements**

Enforces only length ≥ 6. Should require at least a number and a letter for minimum practice.

**Suggestion:**
```pseudo
if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d).{6,}$/', $password)) {
    $error = 'كلمة المرور يجب أن تحتوي على رقم وحرف واحد على الأقل.';
}
```

---

## 2. **Code Quality / Standardization**

### 2.1. **Re-Instantiation of Database Object**

The `$db = new Database();` is called twice: once for token check, again for update. Better to reuse if possible (especially if DB has connection overhead).

**Suggestion:**
```pseudo
// Move $db = new Database(); to top, reuse throughout
$db = new Database();
```

---

### 2.2. **No Input Trimming**

No `trim()` on password fields, possibly causing confusion.

**Suggestion:**
```pseudo
$password = trim($_POST['password']);
$confirm_password = trim($_POST['confirm_password']);
```

---

### 2.3. **Missing HTTP Headers on Redirect**

The `redirect()` function should call `exit;` after the header.

**Suggestion:**
```pseudo
function redirect($url) {
    header('Location: ' . $url);
    exit;
}
```

---

### 2.4. **Hardcoded Strings / Magic Values**

Some error/success messages are hardcoded. Consider using language files, as is done elsewhere, for full consistency.

---

## 3. **User Experience**

### 3.1. **No Autofocus or Autocomplete = 'new-password'**

For a password form, best practice is to set autocomplete and autofocus.

**Suggestion:**
```pseudo
<input ... autocomplete="new-password" autofocus>
```

---

### 3.2. **No Feedback After Success**

On `$success`, consider also revoking any session resets or other tokens.

---

## 4. **General Recommendations**

- Ensure all output to HTML is properly escaped (`htmlspecialchars()`).
- Add comments for understandable sections.
- CSRF should always be required for sensitive actions.
- Add logging for failed/successful password resets for audit.
- Ensure all sensitive logic is tested for edge cases.

---

## 🔧 **Corrected / Suggested Code Snippets (Pseudocode Only)**

```pseudo
// Token validation strict
if (!isset($_GET['token']) || !preg_match('/^[a-f0-9]{64}$/i', $_GET['token'])) {
    $error = 'رمز إعادة التعيين غير صالح';
    $token_valid = false;
} else {
    $token = $_GET['token'];
    // db query...
}

// Use CSRF protection in form:
<input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

// On POST:
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'طلب غير صالح';
        $token_valid = false;
    }
}

// On input processing:
$password = trim($_POST['password']);
$confirm_password = trim($_POST['confirm_password']);

if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d).{6,}$/', $password)) {
    $error = 'كلمة المرور يجب أن تحتوي على حرف ورقم واحد على الأقل.';
}

// On output:
<p><?php echo htmlspecialchars($error, ENT_QUOTES, 'UTF-8'); ?></p>

// Add to form:
<input ... autocomplete="new-password" autofocus>
```

---

## ✅ **Summary Table**

| Issue               | Severity | Recommendation                 |
|---------------------|----------|-------------------------------|
| Token validation    | High     | Regex match length/charset    |
| Unescaped output    | High     | Use `htmlspecialchars()`      |
| CSRF protection     | High     | Add hidden token + verify     |
| Password strength   | Medium   | Regex for complexity          |
| Rate limiting       | Medium   | Implement per-IP delay        |
| Input trimming      | Low      | Use `trim()`                  |
| DB connection reuse | Low      | Use single db instance        |
| Header redirect     | Low      | Ensure `exit;` called         |

---

**Overall:**  
Several security improvements are needed, especially CSRF, output escaping, and strict token validation. Industry-grade software demands these measures, along with robust logging and user experience upgrades.