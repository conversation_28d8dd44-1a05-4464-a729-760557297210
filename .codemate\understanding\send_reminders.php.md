# High-Level Documentation: Event & Reminder Notification Cron Script

## Overview

This PHP script automates sending various types of notifications (reminders, info, cancellations, activity prompts) to users in an event management system (such as "تذاكر فلسطين"). It is designed to be executed periodically, either via a scheduled Cron Job or manually via a web browser.

## Main Functionalities

### 1. Event Reminders (24 Hours Before)
- Sends reminders to users **24 hours before** their confirmed events.
- Notifies only those who haven’t already received a similar reminder today.

### 2. Transport Reminders (1 Hour Before Departure)
- Sends transport (bus, shuttle, etc.) departure reminders **1 hour** before scheduled time to users with confirmed bookings.
- Avoids duplicate reminders for the same event/trip on the same day.

### 3. Expired Notification Cleanup
- Periodically deletes old notifications (of type 'reminder' or 'info') **older than 30 days** from the database.

### 4. Event Cancellation/Postponement Notifications
- If any event is **cancelled or postponed** within the last hour, sends notifications to all ticket holders of that event.
- Only active, confirmed ticket holders are notified.

### 5. Inactive User Activity Reminders
- Sends periodic ("We Miss You") reminders to **active users who haven’t logged in for over 7 days** and have not recently received a similar notification.
- Limited to maximum 50 users per run to prevent system overload.

## Process Flow

1. **Initialization**: Loads required modules, establishes a database connection, and prepares counters/loggers.

2. **Data Selection and Filtering**:
    - Each notification type uses specific SQL queries to identify eligible users and events.
    - Ensures no duplicate reminders by checking existing notification records.

3. **Notification Dispatch**:
    - Utilizes dedicated notification functions for each reminder type (e.g., `notify_event_reminder`, `notify_transport_reminder`, etc.).
    - Tracks success and errors for reporting.

4. **Maintenance Tasks**:
    - Cleans old notifications.
    - Compiles summary statistics for admin awareness.

5. **Output and Logging**:
    - Provides CLI and browser-friendly output summaries.
    - Logs execution outcomes and errors for system administrators.

6. **Exception Handling**:
    - Catches and reports any errors, displaying them in the browser if executed in a web context.

## Usage

- **Automated (CRON)**: Schedule with `php send_reminders.php`, hourly or as needed.
- **Manual**: Can be run via a web browser for testing/admin needs.

Example Cron entry:
```
0 * * * * /usr/bin/php /path/to/your/project/send_reminders.php
```

## Extensibility

- Designed to be modular: additional reminder types or logic can be easily incorporated.
- Notification sending and query logic are separated, following good practices.

## Notes

- All notification queries and sending logic avoid duplication and spamming users.
- Logs important metrics and all errors.
- Supports multi-lingual (Arabic) output for user-facing notifications and messages.

---

**In summary:**  
This script is a key component for event management automation, ensuring timely notifications are sent, reducing manual work, and keeping both users and system data in good standing.