**High-Level Documentation of the Provided PHP Code**

---

**Overview:**

This PHP script handles the creation of a new discount code (coupon) via an AJAX POST request. It validates the request, checks for permissions, sanitizes and validates input data, prevents duplicate codes, manages CSRF protection, inserts the new discount into the database, and returns a JSON response.

---

**Main Responsibilities:**

1. **Session and Dependencies Initialization:**
   - Ensures a session is started if not already.
   - Includes necessary files for database connection and helper functions.
   - Establishes a PDO database connection.
   - Restricts access to administrators only.

2. **Request Handling and Security:**
   - Accepts only POST requests.
   - Expects discount details including code, type, value, usage limit, expiration date, and CSRF token.
   - Sanitizes and type-checks incoming data.
   - Uses a CSRF token for request validation.
   
3. **Input Validation:**
   - Checks for required fields: discount code, type, and valid value.
   - Ensures percentage discount does not exceed 100%.
   - Checks for duplicate discount codes in the database.
   - Accumulates and returns validation errors, if any.

4. **Database Insertion:**
   - On passing validation, inserts the new discount (coupon) into the database (coupons table).
   - Uses prepared statements to prevent SQL injection.

5. **Localization:**
   - Attempts to translate the success message based on the admin’s selected language, if available in session.

6. **Response:**
   - Returns a JSON response indicating success or failure, along with relevant messages and the inserted discount ID if applicable.
   - Handles and logs database errors securely.

---

**Security Considerations:**
- Restricts access to admins.
- Requires and verifies CSRF tokens.
- Sanitizes and validates all input.
- Uses prepared SQL statements.

---

**Typical Use Case:**
This script is likely used in an admin panel for an e-commerce platform, allowing admins to securely create new coupon codes via an AJAX form. Upon successful creation, the frontend receives a JSON response to display appropriate notifications.

---

**Inputs Expected (via POST):**
- `code` (string): Discount code.
- `type` (string): Discount type (e.g., percentage or flat).
- `value` (float): Discount value.
- `usage_limit` (int|null): Maximum number of redemptions (optional).
- `expiration_date` (string|null): Expiry date of the discount (optional).
- `csrf_token` (string): CSRF protection.

**Outputs:**
- JSON response object, with indications of success or failure and corresponding messages.