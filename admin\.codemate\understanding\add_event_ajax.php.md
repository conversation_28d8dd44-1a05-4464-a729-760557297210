## High-Level Documentation

### Overview
This PHP script handles the creation of a new event record in a (presumably) event management system via an AJAX POST request. It ensures secure and validated data processing, handles image uploads, and returns JSON responses indicating success or failure.

---

### Key Features

- **Session & Security**:  
  - Ensures PHP session is started.
  - Requires the user to be an authenticated admin before proceeding.
  - Validates CSRF tokens to prevent cross-site request forgery.

- **Data Validation & Sanitization:**  
  - Expects and sanitizes event-related form fields (title, description, date/time, location, price, type, available tickets, etc.).
  - Verifies required fields are present and valid.
    
- **Image Upload Handling:**  
  - Checks for an image upload.
  - Validates the file is a real image.
  - Moves the uploaded file to a dedicated folder and prepares the file path for database storage.
  - Reports upload errors as needed.
  
- **Database Interaction:**  
  - Uses prepared statements for safe insertion of new event record (helping prevent SQL injection).
  - Dynamically retrieves table columns (mainly for debugging or schema validation).
  - Inserts all provided/validated event fields, including optional image and original price.
  
- **Localization:**  
  - Supports language-based event feedback messages by loading language files based on user session language setting.
  
- **Structured Response:**  
  - Returns all responses as JSON with `success` and `message` keys (plus `event_id` on success).
  - On failure, aggregates and returns validation errors or database errors for client-side feedback.

---

### Flow Summary

1. **Session/authentication and security checks.**
2. **Handles only POST requests.**
3. **CSRF token validation.**
4. **Sanitizes input fields and checks all required fields.**
5. **Handles file upload and image validation.**
6. **On validation failure, sends error JSON and exits.**
7. **Inserts the new event into the database using PDO prepared statements.**
8. **Returns JSON response (success or error), possibly in user's selected language.**

---

### Use cases

- To be used in an admin interface for adding new events (tickets, concerts, courses, etc.) via AJAX.
- Ensures only authenticated admins can add events in a secure and validated manner.
- Handles both event meta-data and (optional) event image upload.

---

### Dependencies

- Database class and connection file.
- Utility and authentication function files.
- Session handling.
- CSRF token verification and sanitization functions.
- Language translation files (for messages).

---

**Note**: This is a high-level summary; the script requires that backend infrastructure (database, functions, uploads directory, etc.) is properly set up and that the admin frontend uses AJAX for submitting new events.