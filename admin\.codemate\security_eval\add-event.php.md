# Security Vulnerabilities Report

*Target*: PHP and JS code for "Add Event" form (with AJAX to `add_event_ajax.php`).

## 1. Error Reporting in Production

```php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```
- **Vulnerability**: Enables full error display, which could expose sensitive information (paths, SQL, configs, etc) in production.
- **Severity**: High (if deployed to production).
- **Recommendation**: Only display errors in development, never in production. Use:  
  ```php
  if (getenv('APP_DEBUG')) { ini_set(...); } else { ini_set('display_errors', 0); }
  ```

---

## 2. Incomplete CSRF Protection Fallback

```php
try {
    $csrf_token = generateCSRFToken();
} catch (Exception $e) {
    error_log("Error generating CSRF token: " . $e->getMessage());
    $csrf_token = md5(uniqid(mt_rand(), true));
}
```
- **Vulnerability**: If CSRF generation fails, falls back to a token (MD5 of random) with no session binding. This can be predictable and bypassable.
- **Severity**: Critical
- **Recommendation**: Fail hard if CSRF cannot be generated. Never create "fake" tokens.  
  ```php
  if (!$csrf_token) { die('CSRF error'); }
  ```

---

## 3. CSRF Token Storage

- **Vulnerability**: The code sets a CSRF token in a hidden field, but it's unclear if the token is validated in `add_event_ajax.php` (code not present). If not verified server-side, CSRF can be exploited.
- **Severity**: Critical
- **Recommendation**: Ensure token is stored in the user’s session and verified in the AJAX handler.

---

## 4. Potential XSS in Output

Examples:
```php
<h1><?php echo $lang['add_event']; ?></h1>
```
- **Vulnerability**: Outputting variables like `$lang[...]` without escaping. If `$lang` is user-modifiable or has unsafe contents, this is a vector for XSS.
- **Severity**: High
- **Recommendation**: Use `htmlspecialchars()` for all user-facing output.  
  ```php
  ... <?php echo htmlspecialchars($lang['add_event']); ?> ...
  ```

---

## 5. Untrusted File Upload (Image)

```html
<input type="file" class="form-control" id="image" name="image" accept="image/*">
```
- **Vulnerability**: File upload is enabled. No evidence of server-side validation of file type/content. Relying on client-side `accept` is insufficient. Attackers may upload executable files masked as images.
- **Severity**: High
- **Recommendation**:  
  - Check MIME type and extension server-side.
  - Use `getimagesize()` or similar to validate actual image.
  - Store outside webroot and serve via proxy or with strict permissions.

---

## 6. Unvalidated/Unsanitized User Input

- **Vulnerability**: This form collects multiple fields (title, description, location, etc.), but there's no clear validation or sanitization (handled in `add_event_ajax.php`, not shown). If inputs are not validated/sanitized/escaped server-side, risks include SQL Injection, XSS, etc.
- **Severity**: Critical to High (depending on downstream code).
- **Recommendation**: Enforce strong validation/sanitization on all user input server-side, regardless of client-side constraints.

---

## 7. Lack of Strict Content Security Policy (CSP)

- **Vulnerability**: The page likely allows arbitrary script execution, and if XSS is present, can be exploited fully.
- **Severity**: Med-High
- **Recommendation**: Add strong CSP headers in the HTTP response.

---

## 8. Information Disclosure (Stack Traces/File Paths)

```php
catch (Exception $e) {
    echo "<div style='...'>Error loading required files: " . $e->getMessage() . "</div>";
```
- **Vulnerability**: On require error, echoing error message (which may contain sensitive paths or information).
- **Severity**: Medium
- **Recommendation**: Log error only; avoid user-facing error details.

---

## 9. Unsecured AJAX Endpoint

- **Vulnerability**: The AJAX call to `add_event_ajax.php` relies on a CSRF token as the only apparent security measure. If CSRF is not correctly implemented, endpoint could be abused.
- **Severity**: High
- **Recommendation**: In `add_event_ajax.php`:
  - Require authentication, authorization, and valid CSRF.
  - Throttle/sanitize input.

---

## 10. Client-Side Validation Only

- **Vulnerability**: All client-side checks (type="number", required) can be bypassed. Server must validate everything.
- **Severity**: General but important.

---

## 11. JavaScript Injection in AJAX Error/Success Response

```javascript
statusMessage.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
```
- **Vulnerability**: If `data.message` is not strictly controlled, an attacker could inject HTML/JS (XSS).
- **Severity**: High
- **Recommendation**: Ensure responses are plain text, or output via `textContent` rather than `innerHTML`, or escape output properly.

---

# Summary Table

| Vulnerability                      | Severity   | Recommendation                             |
|-------------------------------------|------------|--------------------------------------------|
| Error display in production         | High       | Disable error display in prod              |
| Insecure CSRF token fallback        | Critical   | Fail on CSRF error; no fake tokens         |
| CSRF storage/check not shown        | Critical   | Implement session-based CSRF validation    |
| XSS via echo of lang variables      | High       | Escape all output                          |
| Untrusted file upload (images)      | High       | Validate file type/content on server       |
| Lack of server input validation     | Critical   | Enforce server-side validation/sanitization|
| Detailed error output to users      | Medium     | Log errors; generic user messages          |
| JavaScript injection via AJAX resp. | High       | Output via textContent or escape           |
| Weak Content Security Policy        | Med-High   | Add CSP headers                            |
| AJAX endpoint security not shown    | High       | Auth+CSRF+validation on endpoint           |

---

# Final Recommendations

- **Never display raw errors in production.**
- **Implement and enforce strong CSRF protection, never fall back to non-session tokens.**
- **Always escape all output that could be user-controllable, even for labels.**
- **Validate and sanitize all incoming data on the server, regardless of client-side validation.**
- **Secure file uploads at multiple layers: extension, MIME, and file content.**
- **Ensure AJAX endpoints are authenticated and authorize the user.**
- **Do not output detailed error info to users.**
- **Mitigate XSS risks by escaping server messages rendered in the browser.**

---

**Note**: This audit is limited by the code provided. The security of this script also depends on the implementation of `auth_functions.php`, `functions.php`, and `add_event_ajax.php`, which were not reviewed.