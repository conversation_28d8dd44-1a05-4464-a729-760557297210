# High-Level Documentation: C2PA-Embedded PNG File with Provenance Metadata

This file is a PNG image that includes extensive embedded metadata using the [C2PA (Coalition for Content Provenance and Authenticity)](https://c2pa.org/) standard. This format is used to establish a trustworthy record of the image's origin, processing history, and claims about authenticity and content manipulation, often including cryptographic attestations.

## Main Components

### 1. PNG Image Data
- The primary content is a raster PNG image, readable by standard image viewers.

### 2. C2PA JUMBF Boxes (Embedded Metadata)
The binary contains a number of [JUMBF (JPEG Universal Metadata Box Format)](https://www.iso.org/standard/75415.html) containers, with various payloads specific to C2PA metadata.

#### Key Embedded Data Structures:

- **Manifests**
  - Each includes a unique (`urn:c2pa:...`) identifier.
  - Contain references to claims, assertions, content hashes, and ingredients.

- **Assertions**
  - Each assertion provides a "statement" about the content, e.g.
    - Actions performed (created by software agent, conversion).
    - Hashes of content (data integrity).
    - Thumbnail ingredient image (JPEG-compressed subimage/preview).

- **Claims**
  - Metadata about the generator (name/version).
  - Instance identifiers (e.g., `xmp:iid:...` values).
  - Creation times.
  - Assertion references.

- **Signature Containers**
  - Public-key signature blocks, usually X.509-based, to authenticate claims and bind historic statements to the binary.

- **Ingredient Relationship**
  - Describes embedded/related components, such as linked ingredient images.
  - Hashes, titles, manifests, and validation results for ingredient data.

- **Provenance & Validation
  - Chains of trust associating actions with software/actors (e.g., "Created by GPT-4o", processed by "OpenAI API").
  - Validation results for signatures, manifest match, data hash match, etc.

### 3. Hash Containers
- SHA-256 hashes for verifying image or ingredient integrity.
- Used to ensure that referenced binaries or components match the claimed provenance.

### 4. Signatures & Certificates
- Embedded digital signature data (ASN.1 DER encoding, X.509 CA hierarchy).
- Details about signers, certificate chains (e.g., Truepic Lens CLI, OpenAI, etc.).

### 5. Ingredient/Thumbnail Image Data
- Included JPEG thumbnail representing an image ingredient.
- Its own hash, title, data block.

---

## High-Level Workflow

- **Image Created/Edited**: AI or software agent generates or processes image.
- **Metadata Generated**: C2PA manifest with claims, actions, hash assertions, ingredient relationships, and thumbnails.
- **Signing**: Manifest and/or content is digitally signed to create tamper-evident links and foster trust for downstream consumers.
- **Distribution**: PNG with JUMBF-embedded C2PA metadata is published/shared.

---

## Intended Use Cases

- Verifiable media provenance and authenticity.
- Ensuring trust in AI-generated or otherwise manipulated media.
- Forensics and supply chain transparency.
- Enable viewers/tools to query origin, history, and integrity of the content.

---

## Tools & Interoperability

- C2PA-aware viewers, for example:
  - Photoshop, FotoWare, Windows/Adobe applications with C2PA support, or command-line tools like `c2patool`.
- Standard PNG/JPEG/JUMBF parsers extract either the image or the metadata.

---

## Summary

**This file is NOT just an image**: it is a modern, metadata-rich, cryptographically attested PNG that securely records, via C2PA, the image's history, origins, actors involved, and data integrity, leveraging various nested data structures including claims, assertions, ingredients, public-key signatures, and embedded hash validations. This approach is increasingly important for combating misinformation and enabling a verifiable media supply chain.