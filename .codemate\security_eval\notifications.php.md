# Security Vulnerabilities Report

The following report analyzes the provided PHP code for **security vulnerabilities** only.

---

## 1. **Error Display in Production**

```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```
**Issue:**  
Error reporting is enabled, and errors are displayed. This should **never** be enabled in production as it may leak sensitive information about the application's internal structure, database, file paths, etc., to users.

**Recommendation:**  
Disable displaying errors in production. Log errors instead.

---

## 2. **Cross-Site Scripting (XSS)**

### a. **Outputting Unsanitized Values**

Throughout the HTML, application data is echoed directly without sanitization, e.g.:

```php
<img src="<?php echo $user['profile_image']; ?>" alt="<?php echo $user['name']; ?>" ...>
...
<span><?php echo $user['name']; ?></span>
<p class="text-gray-600 text-sm"><?php echo $user['email']; ?></p>
...
<h3 class="font-semibold text-gray-800"><?php echo $notification['title']; ?></h3>
<p class="text-gray-600 text-sm"><?php echo $notification['message']; ?></p>
```

**Risk:**  
If any of these values (`profile_image`, `name`, `email`, notification `title` or `message`, etc.) are not properly sanitized before being output, an attacker could inject malicious JavaScript (XSS vulnerability).

**Recommendation:**
- Sanitize all output using `htmlspecialchars($var, ENT_QUOTES, 'UTF-8')`, especially those which could have been tampered with or come from users or third parties (including notifications).
- For attribute values (like `src` and `alt`), use `htmlspecialchars`.
- Even if you expect these sources to be "safe," always sanitize.

---

## 3. **Cross-Site Request Forgery (CSRF)**

```php
if($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notifications'])) {
    // Updating user notification settings
    ...
}
```

- There is **no CSRF token** in the form for `update_notifications`, so an attacker could construct a malicious site that submits a POST request to this endpoint, changing user notification settings without their consent.

**Recommendation:**
- Incorporate CSRF tokens in all forms that perform state-changing actions.
- Verify these tokens server-side before processing the request.

---

## 4. **Insecure Direct Object Reference (IDOR) and URL Parameter Tampering**

```php
if(isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    $notification_id = (int)$_GET['mark_read'];
    if(mark_notification_read($notification_id, $user_id)) { ... }
...
if(isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $notification_id = (int)$_GET['delete'];
    if(delete_notification($notification_id, $user_id)) { ... }
```

- Direct use of URL parameters to perform state-changing operations (mark as read, delete notification).
- While the functions receive `$user_id` as a parameter, **the actual implementation of `mark_notification_read` and `delete_notification` should check that the notification belongs to the current user** to avoid IDOR (where a user could mark or delete another user's notification by changing the ID).

**Recommendation:**
- Validate in `mark_notification_read()` and `delete_notification()`, at the database level, that the notification belongs to the current authenticated user (using `$user_id`).
- If these functions already do this, document and review that the check is correct. If not, add such checks to prevent privilege escalation.

---

## 5. **Lack of Output Escaping in Links and Attributes**

- In addition to XSS concerns above, attribute values (such as `href`, `alt`, etc.) may also be vectors for XSS or broken HTML.
- Example:
    ```php
    <img src="<?php echo $user['profile_image']; ?>" alt="<?php echo $user['name']; ?>" ...>
    ```
    If `profile_image` contains `javascript:` or malformed URLs, this could be exploited.

**Recommendation:**
- Only allow trusted URL schemes (`https`, `http`) or hash the images in a way that ensures only valid images can be shown.
- Sanitize *all* attributes with `htmlspecialchars`.

---

## 6. **Open Redirection (Not directly found, but...)**

If user-supplied data is ever used in future for `header('Location: ...')` redirects, ensure no open redirection exists. Nothing obvious spotted in this code.

---

## 7. **Session Fixation/Session Handling**

- The code checks for the presence of `'user_id'` in `$_SESSION` to verify login.
- There's no call to `session_regenerate_id()` after login, which helps defend against session fixation attacks.

**Recommendation:**
- After login, call `session_regenerate_id(true);`.

---

## 8. **Lack of HTTP Security Headers**

- Not directly addressed in code. HTTP headers like `X-Content-Type-Options`, `X-Frame-Options`, `Content-Security-Policy` are essential.
- Consider implementing via server config or PHP if possible.

---

## 9. **External Scripts and Styles**

```html
<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/.../font-awesome/6.0.0/css/all.min.css">
```

- The code loads JavaScript and CSS from third-party sources over HTTPS, but **if these scripts were compromised, your site could be compromised** (supply chain attack).

**Recommendation:**
- Where possible, self-host third-party code or whitelist hashes with SRI (`integrity` attribute), and always use the `crossorigin` attribute.

---

## 10. **GET Requests Performing State Changes**

```php
if(isset($_GET['mark_read']) ... )
if(isset($_GET['delete']) ... )
if(isset($_GET['mark_all_read']) ... )
```
- State-changing actions performed via GET requests violate REST principles and can be abused (e.g., via CSRF or search engine bots crawling links).
- No CSRF protection here.

**Recommendation:**
- Only allow GET requests for non-state-changing queries.
- Use POST (with CSRF protection!) for mark as read or delete actions.

---

## 11. **Potential Information Disclosure/Enumeration**

- "No notifications" message is shown, but no enumeration risks are visible in this code. However, audit `get_user_notifications()` to ensure it never exposes data from another user.

---

## 12. **Form Submission – No Input Validation**

- No explicit input validation is done on incoming POST variables, e.g., `$_POST['email_enabled']` etc.
- While only checkboxes (boolean) are accepted, always validate on the server side.

**Recommendation:**
- Strictly validate all inputs.
- Use allow-lists, never rely on user input type.

---

## 13. **No Security on Included Files**

- The code includes several files (`includes/init.php`, etc.). If these files are accessible via the web root, an attacker may download them unless prevented via `.htaccess` or equivalent.

**Recommendation:**
- Place all includes outside of the web-accessible directory, or ensure your server is configured to deny access.

---

# Summary Table

| Issue                                    | Impact            | Recommended Action               |
|------------------------------------------|-------------------|----------------------------------|
| Error display in production              | Info leakage      | Disable error display            |
| XSS via unsanitized output               | Code Injection    | Use `htmlspecialchars`           |
| Lacking CSRF protection                  | Auth bypass       | Add CSRF tokens                  |
| IDOR via notification IDs                | Privilege Escal.  | Authorize notification ownership |
| No output encoding (attributes)          | XSS/HTML breakage | Sanitize all outputs             |
| No session regeneration                  | Session Fixation  | Call `session_regenerate_id()`   |
| HTTP headers missing                     | Several           | Set security headers             |
| External CDN trust                       | Supply-chain      | Use SRI or self-host             |
| GET request state changes                | CSRF/Exploitable  | Use POST for state change        |
| No input validation (POST)               | Code Exploitation | Strict server-side validation    |
| Included file disclosure                 | Info leakage      | Move includes outside webroot    |


---

# Conclusion

**Critical vulnerabilities:** XSS, CSRF, GET state changes, IDOR<br>
**High priority fixes:** Sanitize all output, implement CSRF tokens, switch to POST for state-changing requests, validate notification ownership, harden session management.<br>
**General best-practices:** Disable error display in production, secure includes, enforce headers.

---

## Please audit *all underlying functions* (`get_user_notifications`, etc.) for correct authorization and data escaping, as this is crucial for full system security.

---

**This report addresses only visible code. Additional vulnerabilities may exist in included files or underlying infrastructure.**