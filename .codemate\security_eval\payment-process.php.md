# Security Vulnerability Report

This report details **only security vulnerabilities** found in the provided PHP code snippet. For each issue, a severity rating and recommendations are provided.

---

## 1. Displaying Errors in Production

```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

**Vulnerability:**  
Displaying detailed errors in a production environment discloses sensitive system paths, stack traces, and application internals to users, increasing the risk of targeted attacks.

**Severity:** High

**Recommendation:**  
- Do **not** enable `display_errors` in production. Instead, set `display_errors = 0` and log errors to a protected error log.

---

## 2. SQL Injection (Direct PDO in `create_transport_booking_direct`)

```php
$pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
// ...
$stmt = $pdo->prepare("SELECT id FROM transport_bookings WHERE booking_code = ?");
$stmt->execute([$booking_code]);
// ...
$sql = "INSERT INTO transport_bookings ...";
$stmt = $pdo->prepare($sql);
$result = $stmt->execute([
    $data['user_id'],
    $trip_id,
    $data['event_id'] ?? 1,
    $data['passenger_name'],
    $data['passenger_phone'],
    $data['passengers_count'],
    $data['total_amount'],
    $booking_code
]);
```

**Vulnerability:**  
While prepared statements are used, **user input is passed directly without any server-side validation or type checking**. If an attacker submits unexpected types (e.g., an array or special characters), it could result in unintended queries or application errors.

**Severity:** Medium

**Recommendation:**  
- Enforce **strict server-side validation** and sanitization of all incoming data (`$data['user_id']`, `$trip_id`, etc.).
- Ensure proper data types and filter out unexpected values before binding to statements.

---

## 3. Sensitive Data Exposure (Storing Card Information)

```php
$masked_card_number = str_pad($last_four, strlen($clean_card_number), '*', STR_PAD_LEFT);
// ...
$db->query("INSERT INTO payment_cards (user_id, order_id, card_number, card_holder, expiry_date, amount, status, ip_address, browser, os, device, created_at)
            VALUES (:user_id, :order_id, :card_number, :card_holder, :expiry_date, :amount, 'processed', :ip_address, :browser, :os, :device, NOW())");
// Bind includes full masked card number and expiry date
```

**Vulnerability:**  
- **PCI DSS Violation:** Even though the card number is masked, **writing the expiry date and any form of card info (even masked) into the database may be non-compliant** if not handled securely.
- **CVV is Hashed:** Even storing a hashed CVV is a violation of the PCI DSS: **CVVs must never be stored post-authorization.**

**Severity:** High

**Recommendation:**  
- **Never store CVV, even in hashed/encrypted form. Remove all handling of `$cvv`.**
- Only store the last 4 digits of the card number, and ensure proper compliance for expiry date if stored.
- Regularly audit database access permissions.
- Document PCI DSS compliance procedures.

---

## 4. Cross-Site Scripting (XSS) via User-Supplied Data in Logs/Notifications

```php
$log_message = "طلب حجز جديد: " . $event['title'] . " - المستخدم: " . $_SESSION['user_name'] . " - المبلغ: " . $total_amount . " ₪";
error_log($log_message);
// ...
add_notification($_SESSION['user_id'], ..., $notification_message, ...);
```

**Vulnerability:**  
- **Lack of output encoding/escaping** when using user-controlled variables in logs and notifications (`$event['title']`, `$_SESSION['user_name']`), which can be reflected in logs or UI if later rendered, facilitating XSS.

**Severity:** Medium

**Recommendation:**  
- Ensure all user-supplied strings are **escaped/sanitized** before being used in notifications, logs that might be output in UIs, or emails.

---

## 5. Session Data Trust Without Integrity Checks

```php
$transport_booking = $_SESSION['booking_data'];
```

**Vulnerability:**  
Relies on session data for important information (like `$transport_booking`) without validating integrity or freshness. If session hijacking or manipulation occurs, attackers may inject arbitrary data.

**Severity:** Medium

**Recommendation:**  
- Validate all session data thoroughly before use.
- Consider implementing server-side integrity checks or checksums.

---

## 6. Insecure Direct Object References (IDOR)

```php
$event_id = $_POST['event_id'];
// ...
$event = get_event_by_id($event_id);
```

**Vulnerability:**  
- **No ownership or authorization check** is performed after fetching `$event_id` from the client. Malicious users can supply arbitrary event IDs to potentially access or reserve forbidden events.

**Severity:** Critical (if sensitive events are present)

**Recommendation:**  
- Enforce **authorization checks** to ensure the current user has permission to book or view the specified event.

---

## 7. CSRF (Cross-Site Request Forgery) Protection Missing

**Vulnerability:**  
There is **no evidence of CSRF tokens** being required or checked for the POST request. This allows attackers to force logged-in users to perform disruptive actions.

**Severity:** High

**Recommendation:**  
- **Implement CSRF tokens** in forms and verify on POST requests.

---

## 8. Information Leakage via URL Parameters

```php
header("Location: checkout.php?event_id={$event_id}");
header("Location: payment-processing.php?event_id={$event_id}&quantity={$quantity}&order_id={$result['order_id']}");
```

**Vulnerability:**  
Sensitive information (e.g., internal IDs, order references) are transmitted via GET parameters, which may be logged in server access logs, browser history, or leaked via referrers.

**Severity:** Medium

**Recommendation:**  
- Consider referencing entities via short-lived, non-predictable tokens.
- Minimize the use of sensitive IDs in URLs.

---

## 9. Predictable ID and Token Generation

```php
$order_id = 'ORDER_' . time() . '_' . $user_id;
$ticket_codes[] = 'TICKET_' . strtoupper(substr(md5(time() . $i . rand()), 0, 10));
$booking_code = 'TR' . strtoupper(substr(md5(time() . rand()), 0, 8));
```

**Vulnerability:**  
Predictable order IDs, ticket codes, and booking codes (based on `time()`, incremental `$i`, and `rand()`) are susceptible to guessing and brute-force enumeration.

**Severity:** High

**Recommendation:**  
- Use **cryptographically secure random functions** (e.g., `random_bytes()` and `bin2hex()`) for ID/token generation.
- Avoid including predictable data such as timestamps or user IDs in security tokens.

---

## 10. No Rate Limiting / Brute Force Protection

**Vulnerability:**  
There is **no evidence of rate limiting or brute-force protection** for booking attempts, payment process, or notification triggers. Attackers may exploit this for denial-of-service or brute-force attacks.

**Severity:** Medium

**Recommendation:**  
- Implement IP or user-based rate limiting, especially on payment and booking endpoints.

---

## 11. No HTTPS Enforcement

**Vulnerability:**  
The code does not check or enforce HTTPS for forms handling sensitive data, exposing credentials and card data to interception.

**Severity:** Critical

**Recommendation:**  
- Enforce HTTPS on all endpoints dealing with authentication or payments.
- Redirect incoming HTTP requests to HTTPS automatically.

---

## 12. Weak Password/Session Management

**Vulnerability:**  
There is no evidence of robust session management, e.g., regenerating session IDs post-login, strong cookie flags (`HttpOnly`, `Secure`).

**Severity:** High

**Recommendation:**  
- Ensure sessions are set with `HttpOnly` and `Secure` flags.
- Regenerate session IDs after login.
- Implement session timeout and logout mechanisms.

---

# Summary Table

| Vulnerability                                   | Severity    | Recommendation                             |
|-------------------------------------------------|-------------|--------------------------------------------|
| Displaying Errors in Production                 | High        | Disable display_errors, only log to file   |
| SQL Injection Potential Data Handling           | Medium      | Server-side data validation/filtering      |
| Sensitive Data Exposure (PCI DSS Violation)     | High        | Never store CVV, mask cards appropriately  |
| XSS via Logs/Notifications                     | Medium      | Output encode/escape all user data         |
| Session Data Trust Without Checks               | Medium      | Validate session data content              |
| IDOR (Authorization Checks)                     | Critical    | Enforce checks on resource ownership       |
| CSRF Protection Missing                        | High        | Implement CSRF tokens in POST forms        |
| Info Leakage via URL Parameters                 | Medium      | Limit sensitive data in URLs               |
| Predictable IDs/Codes                           | High        | Use cryptographically safe random generators|
| No Brute Force/Rate Limiting                    | Medium      | Add rate limits on sensitive endpoints     |
| No HTTPS Enforcement                            | Critical    | Enforce HTTPS for all sensitive endpoints  |
| Weak Password/Session Management                | High        | Apply robust session management practices  |

---

# **Summary**

This PHP code contains several **serious security vulnerabilities** that could result in data loss, payment fraud, and loss of user trust. Immediate remediation is required, especially for PCI DSS compliance, authorization enforcement, CSRF protection, token/id randomness, session management, and HTTPS enforcement.

**Do not deploy such code to production without first addressing the above vulnerabilities.**