# Security Vulnerability Report

## Code Analyzed

PHP file for a payment error page. The code displays an error message stored in a session variable, shows basic event information if provided via a GET parameter, and provides navigation and help links. Some JavaScript is used for UI effects.

---

## Summary Table

| Location                            | Vulnerability                                    | Severity  | Description                                                            |
|--------------------------------------|--------------------------------------------------|-----------|------------------------------------------------------------------------|
| Display Errors Enabled (line 3-5)    | Information Disclosure                            | Medium    | PHP errors may expose sensitive info to users.                         |
| `event_id` from `$_GET` (line 13)    | Unfiltered External Input / Potential Injection   | High      | No validation/sanitation on `$_GET['event_id']`.                       |
| Rendering `event_id` in URLs         | Reflected Input (HTML context)                    | Medium    | Unsafe rendering of `event_id` in URLs – potential for injection.      |
| Exception Message in `die()`         | Information Disclosure                            | Medium    | Exposing exception messages to the user.                               |
| Unset Only `$_SESSION['error_message']` | Session Data Leakage                            | Low/Info  | Other sensitive session data may persist/leak between requests.        |
| Outputting Event Data                | Trust in `get_event_by_id()` Output               | Unknown   | Assumes escaping occurs. If not, risk of XSS/data injection.           |

---

## Detailed Findings

### 1. Display Errors Enabled

**Location:** 
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```
**Issue:**
- `display_errors = 1` will show *all* PHP errors and warnings to the user in the browser. This can leak file paths, stack traces, database queries, and other sensitive information, **especially in production environments**.

**Recommendation:**
- Set `display_errors = 0` in production.
- Use `log_errors` for diagnostics.

---

### 2. Use of Unfiltered `$_GET['event_id']`

**Location:** 
```php
$event_id = $_GET['event_id'] ?? null;
...
if ($event_id) {
    $event = get_event_by_id($event_id);
}
```
- `event_id` comes directly from the user, with no validation or sanitization.

**Risks:**
- **SQL Injection:** If `get_event_by_id` uses this value in a database query without proper parameterization/escaping, this is a high severity issue.
- **Type Juggling/Parameter Pollution:** An attacker could pass unexpected types (arrays, objects) or string content that causes unexpected behavior.

**Recommendation:**
- **Validate type** (e.g., integer: `filter_var($event_id, FILTER_VALIDATE_INT)` or similar).
- In `get_event_by_id()`, always use parameterized queries.

---

### 3. Rendering `event_id` in URLs (Reflected Input)

**Location:**
```php
<a href="checkout.php?event_id=<?php echo $event_id; ?>"
```
```php
<a href="event-details.php?id=<?php echo $event_id; ?>"
```
- Since `$event_id` is used directly in URL output and comes from the user, if not properly validated, this can allow HTML/JS injection in rare cases (depends on context and browser behavior).

**Example Attack:**
- User requests: `?event_id="onmouseover%3dalert(1)%20bad=1`
- Output: `<a href="checkout.php?event_id="onmouseover=alert(1) bad=1">...`
- While it's inside an attribute value, some browsers may interpret the malformed HTML as untrusted script.

**Recommendation:**
- **Cast to integer:** Ensure `$event_id = (int)$event_id;` before using.
- **Escape in URLs:** Use `urlencode($event_id)` in query parameters.
- Combine with server-side validation.

---

### 4. Exception Message Disclosure

**Location:** 
```php
catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}
```
**Issue:**
- Exposing raw exception messages to users can reveal stack traces, class names, database errors, etc.

**Recommendation:**
- Log the full exception to an error log or monitoring system.
- Show generic messages to the user: `"An error occurred, please try again later."`

---

### 5. Assumed Escaping of Event Data

**Location:** 
```php
<p class="text-gray-600"><?php echo htmlspecialchars($event['title']); ?></p>
...
<?php echo date('Y-m-d H:i', strtotime($event['date_time'])); ?>
```
- *Title* uses `htmlspecialchars` (correct).
- `date('Y-m-d H:i', strtotime($event['date_time']))` not an issue if `date_time` is a valid date (should be validated in `get_event_by_id`).

**Consideration:**
- All output from database/user input must be escaped in the appropriate context (HTML, attributes, JS).

---

### 6. Session Data Handling (Low/Info)

- Code only unsets `$_SESSION['error_message']` (good practice).
- Ensure other sensitive session info is unset as needed on other pages.

---

### 7. JavaScript/DOM Manipulation

- No dangerous use of untrusted user input in JS context detected.
- All DOM manipulations are UI-only, not using or reflecting user data.

---

## Recommendations Overview

1. **Never display errors in production.** Set `display_errors=0`; always log instead.
2. **Strictly validate** all GET/POST parameters. For `event_id`, enforce integer type.
3. **Escape all output** for its context (HTML, attribute, JS, URL).
4. **Never show raw exception messages** to end users.
5. **Confirm** `get_event_by_id()` is secure and not vulnerable to SQL Injection or similar attacks.
6. **Audit all includes** (`init.php`, `functions.php`, etc.) for similar issues – especially how events are fetched.
7. **Maintain minimal session footprint** to avoid information leak.

---

## Example: Secure Handling for event_id

```php
// Securely fetch event id
$event_id = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);
if (!$event_id) { $event_id = null; }
```
Using in URLs:
```php
<a href="checkout.php?event_id=<?php echo urlencode($event_id); ?>">
```
And in data queries:
- Make sure your database query uses parameterized statements.

---

## Summary

**Critical risk areas:**
- Use of unvalidated user input (`event_id` from `$_GET`) — *HIGH* if database queries are not properly parameterized.
- Error reporting settings — *INFO LEAK* risk in production.
- Exception message display — *INFO LEAK*.

**No critical XSS or CSRF observed in this snippet**, but code hygiene, input validation, and output escaping must be carefully reviewed, including all includes and data-fetching functions.

---