```markdown
# Critical Code Review Report

## Overview

This report summarizes **critical issues** found in the provided PHP code implementing an automated notification/reminder CRON job. The code is reviewed for:

- Security (SQL injections, data handling)
- Performance/optimization issues
- Maintainability/readability
- Logic/bugs
- Coding standards

Only **modified/suggested code lines are shown below as pseudo code** to aid correction.

---

## 1. Database Query Security (SQL Injection)

**Issue:**
Direct SQL queries with string concatenation can introduce SQL injection flaws. (While this code does not inject user data, it's an *anti-pattern*.)

**Suggestion:**  
Use parameterized queries (prepared statements).

**Replace**  
```php
$db->query("SELECT ... WHERE e.date_time BETWEEN NOW() + INTERVAL 23 HOUR AND NOW() + INTERVAL 25 HOUR ...");
```
**With**
```pseudo
$db->query("SELECT ... WHERE e.date_time BETWEEN NOW() + INTERVAL :start HOUR AND NOW() + INTERVAL :end HOUR ...");
$db->bind(':start', 23);
$db->bind(':end', 25);
```
_**Apply for all similar queries** (events, trips, notifications cleanup, etc) even if no direct user input is present, to force best practice in codebase._

---

## 2. Inefficient `LIKE` Clauses in Subselects

**Issue:**
Use of  
```sql
AND n.message LIKE CONCAT('%', e.title, '%')
```  
prevents use of indexes and will hurt scalability.

**Suggestion:**  
Store unique event/trip IDs in notifications table as a dedicated column (e.g. `entity_id`), and query with  
```sql
AND n.entity_id = e.id
```
**Pseudo code fix:**
```pseudo
-- Add entity_id (event_id/trip_id) in notifications table
ALTER TABLE notifications ADD entity_id INT;

-- When creating notification
add_notification(user_id, ..., entity_id=event['id'], ...);

-- Update relevant SELECT and subselect to use entity_id
AND n.entity_id = e.id
```
_**If table refactor not possible, document the performance impact for future refactoring.**_

---

## 3. Unoptimized `GROUP_CONCAT` + `explode`

**Issue:**
```php
$user_ids = array_map('intval', explode(',', $event['user_ids']));
```
For large data sets, this is less efficient.

**Suggestion:**  
Just select user IDs as rows and process the results in a single pass.

**Pseudo code fix:**  
```pseudo
-- Instead of GROUP_CONCAT in SQL
SELECT t.user_id ... FROM tickets t ... WHERE ...;

-- Group by event in PHP:
$users_per_event = [];
foreach ($changed_events as $event) {
    $users_per_event[$event['id']][] = $event['user_id'];
}
-- Then, process notifications per event.
```
_**Or, factor notification-sending to operate per user row rather than grouping users.**_

---

## 4. Redundant `$db->query()` Before `$db->execute()`

**Issue:**
```php
$db->query("DELETE FROM notifications ...");
$db->execute();
```
If `$db->query()` already executes, then a separate `$db->execute()` is redundant (depends on DB helper implementation).

**Suggestion:**  
Use either:
- `$db->query()` sets and executes (if that's your implementation), OR
- `$db->prepare()` and then `$db->execute()` (if using PDO style), but **not both**.

**Pseudo code fix:**
```pseudo
$db->query("DELETE FROM notifications ..."); // If this runs immediately, remove $db->execute()
```
-OR-
```pseudo
$db->prepare("DELETE FROM notifications ...");
$db->execute();
```
_**Check your Database class!**_

---

## 5. Missing Logging for All Errors

**Issue:**  
Currently, only summary errors logged at the end; individual failed notifications are only echoed.

**Suggestion:**  
Log all notification errors for audit.

**Pseudo code fix:**
```pseudo
foreach $errors as $error:
    error_log($error);
```

---

## 6. Lack of Rate Limiting / Idempotency

**Issue:**  
A crashed or repeated run could send duplicate notifications.

**Suggestion:**  
Insert a unique constraint (if not present) on `(user_id, type, entity_id, created_at)` in notifications.  
Or, in notification sending, check right before sending:

**Pseudo code:**
```pseudo
if (!notification_already_sent(user_id, type, entity_id, date)):
    send_notification(...)
```

---

## 7. Uninitialized Variables (Strict Mode)

**Issue:**  
If no expired notifications, `$expired` might be undefined depending on DB result.

**Suggestion:**  
Always initialize variables from DB with default fallback.

**Pseudo code:**
```pseudo
$expired = $db->single() ?: ['expired_count' => 0];
```
---

## 8. HTML Output Mixed With CLI Output

**Issue:**  
Mixing echo for CLI and browser. For future maintainability, extract all presentation logic to **view functions**.

**Suggestion:**  
Refactor output code to separate functions depending on SAPI.

**Pseudo code:**
```pseudo
function output_cli($msg) { echo $msg."\n"; }
function output_html($msg) { echo "<br>$msg"; }
if (php_sapi_name() === 'cli') { output_cli(...) } else { output_html(...); }
```
---

## 9. Hardcoded "events.php" Link

**Issue:**  
Hardcoded internal link is fragile.

**Suggestion:**  
Store such URLs in config/constants.

**Pseudo code:**
```pseudo
$link = SITE_URL . "/events.php";
```
---

## 10. Magic Numbers Without Documentation

**Issue:**  
Raw intervals like `23`, `25`, `70` magic numbers obscure meaning.

**Suggestion:**  
Define as constants.

**Pseudo code:**
```pseudo
define('EVENT_REMINDER_HOURS_BEFORE', 24);
define('TRANSPORT_REMINDER_MINUTES_BEFORE', 60);
-- Use in queries:
... INTERVAL :event_reminder HOUR ...
$db->bind(':event_reminder', EVENT_REMINDER_HOURS_BEFORE);
```
---

## Summary Table

| Issue                                         | Severity | Fix Status  |
|------------------------------------------------|----------|-------------|
| Unsafe (non-param) SQL queries                 | High     | See #1      |
| LIKE-based matching on event titles            | Med      | See #2      |
| GROUP_CONCAT inefficiency                      | Low      | See #3      |
| Redundant DB execution steps                   | Med      | See #4      |
| Missing error logging for individual failures  | Med      | See #5      |
| No idempotent check/rate limit                 | High     | See #6      |
| DB fetch may return undefined values           | Low      | See #7      |
| Mixed output for CLI/HTML                      | Low      | See #8      |
| Hardcoded link strings                         | Low      | See #9      |
| Magic numbers                                  | Low      | See #10     |

---

**Conclusion:**  
Most errors are performance and maintainability related, but security best practices (param queries, idempotency) must be enforced for a production system.

_Please implement the suggested code changes in the appropriate lines in your source code._
```
