# High-Level Documentation

## Overview

This PHP script handles AJAX requests to update the payment status of an order associated with a ticket in a ticketing or order management system. The code is secured for admin use, performs user authentication, data validation, CSRF (Cross-Site Request Forgery) protection, and updates the database accordingly while returning a structured JSON response.

---

## Main Features & Flow

1. **Session Management & Imports**
   - Ensures a PHP session is started.
   - Includes database connection and utility/authentication function files.

2. **Security & Authentication**
   - Requires the user to be an admin (`requireAdmin`).
   - Uses CSRF token validation to prevent unauthorized requests.

3. **Request Handling**
   - Only accepts POST requests.
   - Retrieves and sanitizes required data: `ticket_id`, `status`, and `csrf_token`.
   - Validates input data (ensures ticket ID and status are valid).

4. **Business Logic**
   - Retrieves the order ID associated with the provided ticket ID.
   - Updates the payment status of the found order in the database.

5. **Localization**
   - If a language file is set in the session, will localize the status text in the response (e.g., "completed", "pending", "cancelled") using language files.

6. **Response**
   - Returns a JSON response indicating success/failure, relevant messages, and the updated status.

7. **Error Handling**
   - Catches and reports database (PDO) errors, logging details and always returning a safe, user-friendly JSON message.

---

## Typical Use Case

This endpoint is typically triggered by an admin interface via AJAX when changing the payment status for an order based on a support ticket (for example, marking a ticket's order as “completed” once payment is verified). It ensures that only authorized personnel can perform this action, and it provides localized status messages for internationalized admin interfaces.

---

## Security Measures

- **Session-based authentication** limiting execution to administrators only.
- **CSRF protection** to prevent cross-site request forgery.
- **Input validation and sanitization** to mitigate injection attacks.
- **Database error logging** without exposing raw error messages to the client.

---

## Inputs

- `ticket_id` (int): Ticket identifier.
- `status` (string): New payment status (`completed`, `pending`, `cancelled`, etc.).
- `csrf_token` (string): Security token for CSRF protection.

## Outputs

Returns a JSON object:
- `success`: Boolean status of the operation.
- `message`: Human-readable message (localized, if available).
- `ticket_id`: The ticket affected (on success).
- `new_status`: The updated status (on success).
- `status_text`: Localized version of the updated status (on success).

---

## Pre-requisites

- Admin user authentication and an active session.
- CSRF token mechanism must be set up in forms and session.
- Proper database structure with `tickets` and `orders` tables.
- Language files for status localization (optional).

---

## Example Response

```json
{
  "success": true,
  "message": "Ticket status updated successfully",
  "ticket_id": 123,
  "new_status": "completed",
  "status_text": "تم الإنجاز"
}
```

---

**Note:**  
This script is intended for back-end use in a secure environment and should be connected to a properly configured admin panel or API frontend.