<?php
require_once '../../includes/init.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_POST['action'] ?? '';
$db = new Database();

try {
    switch ($action) {
        case 'delete':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // التحقق من وجود رحلات مرتبطة بهذه النقطة
            $db->query("SELECT COUNT(*) as count FROM transport_trips WHERE starting_point_id = :id");
            $db->bind(':id', $id);
            $result = $db->single();
            
            if ($result['count'] > 0) {
                throw new Exception('لا يمكن حذف نقطة الانطلاق لأنها مرتبطة برحلات موجودة');
            }

            // حذف نقطة الانطلاق
            $db->query("DELETE FROM transport_starting_points WHERE id = :id");
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم حذف نقطة الانطلاق بنجاح']);
            } else {
                throw new Exception('فشل في حذف نقطة الانطلاق');
            }
            break;

        case 'update':
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $region = trim($_POST['region'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }
            if (empty($name)) {
                throw new Exception('اسم نقطة الانطلاق مطلوب');
            }
            if (empty($region)) {
                throw new Exception('المنطقة مطلوبة');
            }

            // التحقق من عدم تكرار الاسم
            $db->query("SELECT id FROM transport_starting_points WHERE name = :name AND id != :id");
            $db->bind(':name', $name);
            $db->bind(':id', $id);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('اسم نقطة الانطلاق موجود بالفعل');
            }

            // تحديث نقطة الانطلاق
            $db->query("
                UPDATE transport_starting_points 
                SET name = :name, description = :description, region = :region, is_active = :is_active, updated_at = NOW()
                WHERE id = :id
            ");
            $db->bind(':name', $name);
            $db->bind(':description', $description);
            $db->bind(':region', $region);
            $db->bind(':is_active', $is_active);
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث نقطة الانطلاق بنجاح']);
            } else {
                throw new Exception('فشل في تحديث نقطة الانطلاق');
            }
            break;

        case 'add':
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $region = trim($_POST['region'] ?? '');
            $icon = trim($_POST['icon'] ?? 'map-marker-alt');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if (empty($name)) {
                throw new Exception('اسم نقطة الانطلاق مطلوب');
            }
            if (empty($region)) {
                throw new Exception('المنطقة مطلوبة');
            }

            // التحقق من عدم تكرار الاسم
            $db->query("SELECT id FROM transport_starting_points WHERE name = :name");
            $db->bind(':name', $name);
            $existing = $db->single();
            
            if ($existing) {
                throw new Exception('اسم نقطة الانطلاق موجود بالفعل');
            }

            // إضافة نقطة انطلاق جديدة
            $db->query("
                INSERT INTO transport_starting_points (name, description, region, icon, is_active, created_at, updated_at)
                VALUES (:name, :description, :region, :icon, :is_active, NOW(), NOW())
            ");
            $db->bind(':name', $name);
            $db->bind(':description', $description);
            $db->bind(':region', $region);
            $db->bind(':icon', $icon);
            $db->bind(':is_active', $is_active);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة نقطة الانطلاق بنجاح']);
            } else {
                throw new Exception('فشل في إضافة نقطة الانطلاق');
            }
            break;

        case 'get':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            $db->query("SELECT * FROM transport_starting_points WHERE id = :id");
            $db->bind(':id', $id);
            $point = $db->single();
            
            if (!$point) {
                throw new Exception('نقطة الانطلاق غير موجودة');
            }

            echo json_encode(['success' => true, 'data' => $point]);
            break;

        default:
            throw new Exception('عملية غير مدعومة');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
