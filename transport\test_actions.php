<?php
// ملف اختبار للتأكد من عمل العمليات بشكل صحيح
require_once '../includes/init.php';

echo "<h2>اختبار اتصال قاعدة البيانات</h2>";

$db = new Database();

try {
    // اختبار جلب نقاط الانطلاق
    echo "<h3>نقاط الانطلاق:</h3>";
    $db->query("SELECT * FROM transport_starting_points LIMIT 5");
    $points = $db->resultSet();
    foreach ($points as $point) {
        echo "- " . htmlspecialchars($point['name']) . " (" . $point['region'] . ")<br>";
    }

    // اختبار جلب السائقين
    echo "<h3>السائقين:</h3>";
    $db->query("SELECT * FROM transport_drivers LIMIT 5");
    $drivers = $db->resultSet();
    foreach ($drivers as $driver) {
        echo "- " . htmlspecialchars($driver['name']) . " - " . $driver['phone'] . "<br>";
    }

    // اختبار جلب الرحلات
    echo "<h3>الرحلات:</h3>";
    $db->query("SELECT * FROM transport_trips LIMIT 5");
    $trips = $db->resultSet();
    foreach ($trips as $trip) {
        echo "- رحلة #" . $trip['id'] . " - السعر: " . $trip['price'] . " ₪<br>";
    }

    // اختبار جلب أنواع وسائل النقل
    echo "<h3>أنواع وسائل النقل:</h3>";
    $db->query("SELECT * FROM transport_types WHERE is_active = 1");
    $types = $db->resultSet();
    foreach ($types as $type) {
        echo "- " . htmlspecialchars($type['name']) . "<br>";
    }

    // اختبار جلب الفعاليات
    echo "<h3>الفعاليات:</h3>";
    $db->query("SELECT * FROM events WHERE is_active = 1 LIMIT 5");
    $events = $db->resultSet();
    foreach ($events as $event) {
        echo "- " . htmlspecialchars($event['title']) . "<br>";
    }

    echo "<br><strong style='color: green;'>✅ جميع الاختبارات نجحت!</strong>";

} catch (Exception $e) {
    echo "<strong style='color: red;'>❌ خطأ: " . $e->getMessage() . "</strong>";
}

echo "<br><br><a href='dashboard.php'>العودة إلى لوحة التحكم</a>";
?>
