## High-Level Documentation

### Purpose

This PHP code provides an **administrative dashboard page** for a web application, focused on displaying and managing **user activity logs**. It is only accessible to administrators and includes two main data tables:

- **Login Logs:** All user login attempts with detailed metadata.
- **Registration Logs:** All user registrations with similar metadata.

### Main Functionalities

1. **Authentication and Authorization**
   - Ensures the user is logged in and holds administrative privileges.
   - Redirects unauthorized users to the login page.

2. **Database Operations**
   - Establishes a secure database connection.
   - Retrieves all login and registration event logs by joining log tables with user information for reporting.

3. **Dynamic Data Presentation**
   - Displays the fetched logs in responsive, feature-rich HTML tables.
   - Each table includes columns: ID, User Name, Email, IP Address, Browser, OS, Device Info, and Timestamp (Login or Registration time).

4. **User Interface**
   - Integrates with standardized header and footer templates for consistent look and feel.
   - Utilizes DataTables (JavaScript library) for advanced table features: sorting, searching, and responsive design.
   - Tables are default-sorted by the most recent events.
   - Full Arabic localization for UI elements and table content.

5. **Security and Usability**
   - HTML output is safely escaped to prevent XSS vulnerabilities.
   - Timestamps are formatted for readability.
   - Ensures only authorized personnel can access sensitive activity logs.

---

### Key Components

- **Files Included**
  - Configuration, database, helper functions, and authentication classes.
  - Shared header and footer markup.

- **Database Tables (Implied)**
  - `login_logs`: Records of user login activity.
  - `registration_logs`: Records of user sign-up activity.
  - `users`: Master user information, joined to logs for name/email attribution.

- **Tables Rendered**
  - **Login Logs Table:** Shows who logged in, from where, using what device and software, and when.
  - **Registration Logs Table:** Shows who registered, and their metadata at signup.

- **JavaScript**
  - Initializes DataTables for both log views.
  - Loads Arabic language packs for localization.

---

### Target Audience

- **Site Administrators:** Staff with permissions to audit or analyze security-related user events.

---

### Customization

- Can be extended to include filtering, exporting, or deeper analytics as needed.
- UI and localization are easily adaptable for different languages or branding.

---

**In summary:**  
This code is a secure, admin-only page for viewing comprehensive logs of user logins and registrations, giving administrators visibility and auditing power over user activities within the application.