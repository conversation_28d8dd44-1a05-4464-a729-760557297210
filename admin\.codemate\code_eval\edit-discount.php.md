# Code Review Report

## 1. **Error Handling & Data Integrity**

### a) No `$pdo` Initialization or Error Handling

- **Issue**: Code assumes `$pdo` is set/initialized. There is no check or error handling if `$pdo` is missing or connection fails.
- **Recommendation**: Check if `$pdo` is defined/connected correctly.

**Suggested code:**
```php
// Pseudo-code: At the top, after includes
if (!isset($pdo)) {
    // Log error and show friendly message
    error_log('PDO connection missing in edit_discount.php');
    die('Database connection error. Please contact administrator.');
}
```

---

### b) Unnecessary/Incorrect SQL Field Aliasing

- **Issue**: `SELECT *, expiry_date as expiration_date, 0 as usage_count, 1 as is_active FROM ...` is inefficient:
  - `expiry_date as expiration_date`: Redundant if already `expiry_date`.
  - `0 as usage_count`, `1 as is_active`: Returns static values, not from DB.
- **Recommendation**: Select only needed fields; alias only for actual columns.

**Suggested code:**
```php
$stmt = $pdo->prepare("SELECT id, code, type, value, usage_limit, usage_count, expiry_date FROM coupons WHERE id = :id");
```

---

### c) Potential XSS – Inadequate Output Escaping

- **Issue**: Usage of `echo $discount['value']` and similar. Only `code` uses `htmlspecialchars`, the rest do not.
- **Recommendation**: Always output with `htmlspecialchars()` unless the variable is known safe numeric.

**Suggested code:**
```php
// E.g. Modify:
value="<?php echo htmlspecialchars($discount['value']); ?>"
// ...same for all user-supplied values output in the HTML.
```

---

### d) Usage of Undefined Variables/Columns

- **Issue**: Form tries to show `$discount['usage_limit']`, `$discount['usage_count']`, etc.
  - If DB doesn't have those, will result in `Undefined index`.
- **Recommendation**: Validate array keys exist, check schema matches.

**Suggested code:**
```php
<input ... value="<?php echo isset($discount['usage_limit']) ? htmlspecialchars($discount['usage_limit']) : ''; ?>">
```

---

### e) CSRF Token Storage

- **Issue**: The code generates `$csrf_token = generateCSRFToken();` but does not indicate storage in session. This is only safe if the helper does this internally.
- **Recommendation**: Ensure CSRF token is stored in session for later validation in `update_discount.php`.

---

## 2. **Security**

### a) No Authorization/Role Checks after login

- **Issue**: Code calls `requireAdmin()`, but does not check if function outputs error/redirect or continues. Failure should result in exit.
- **Recommendation**: Ensure `requireAdmin()` halts on fail. Consider explicit check.

---

### b) HTTP Strict Transport Security (HSTS)

- **Issue**: The code does not enforce HTTPS.
- **Recommendation**: Add HSTS header or redirect to HTTPS.

**Suggested code:**
```php
if (empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off") {
    header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);
    exit;
}
// Or, header('Strict-Transport-Security: max-age=63072000; includeSubDomains; preload');
```

---

## 3. **UX/UI Concerns**

### a) No Server-side Input Validation

- **Issue**: AJAX posts the form, but no server-side checks are shown for required fields or valid discount type. Reliance only on front-end validation is insufficient.
- **Recommendation**: Add validation in `update_discount.php`.

---

### b) Lack of Feedback on Expiry/Usage

- **Issue**: Displaying static usage count (`0 as usage_count`). Should be dynamically loaded from DB.
- **Recommendation**: Use real DB value as shown above.

---

## 4. **Javascript/Frontend Flaws**

### a) No Form Disable During Submission

- **Issue**: User may double-submit during AJAX.
- **Recommendation**: Disable the form or button during request.

**Suggested code:**
```javascript
saveBtn.disabled = true;
// ...on success/fail (in both .then and .catch)
saveBtn.disabled = false;
```

---

### b) No Client-side Validation for Input Range

- **Issue**: Value input allows all `min=0`, including 0 (%) which may not be valid for a discount.
- **Recommendation**: Enforce logic e.g., min value for percentage.

**Suggested code:**
```html
<input ... id="value" name="value" min="0.01" ...>
```

---

## 5. **General Maintainability**

### a) Magic Numbers and Strings

- **Issue**: `"₪"`, `"%"`
- **Recommendation**: Move currency/symbols to config or i18n files.

---

### b) Language Fallback

- **Issue**: Assumes `$lang[...]` always set. Missing keys will cause warnings.
- **Recommendation**: Provide fallback.

**Suggested code:**
```php
// E.g.
<?= isset($lang['edit_discount']) ? $lang['edit_discount'] : 'Edit Discount' ?>
```

---

## 6. **Performance/Optimization**

### a) * Queries and Unused Fields

- **Issue**: `SELECT *` is slower and may open attack surface (data leakage).
- **Recommendation**: See earlier, select only needed fields.

---

### b) Multiple Includes can be optimized

- **Issue**: Redundant includes (e.g., includes/admin_header.php AND ../includes/auth_functions.php) – verify structure avoids duplication/conflicts.

---

# **Summary Table**

| Area          | Issue                                                                     | Priority | Suggested Fix (see details above)         |
|---------------|--------------------------------------------------------------------------|----------|-------------------------------------------|
| Database      | Unchecked PDO connection                                                 | High     | Add check for $pdo                       |
| Security      | Static values in SQL, possible XSS, no CSRF session handling shown       | High     | Improve SQL, only escape HTML, check CSRF |
| Logic         | Undefined index if field missing                                         | Medium   | Use isset()                              |
| UX            | No disable during AJAX, possible double submission                       | Medium   | Disable button during AJAX                |
| Validation    | No server-side input validation                                          | High     | Add in `update_discount.php`              |
| Frontend      | No input min/step logic checked for 'value'                              | Medium   | Adjust min to non-zero for percent        |
| Maintain      | Magic strings/currency/language fallback                                | Low      | Use config/i18n fallback                  |
| Security      | No HTTPS enforcement                                                     | Low      | Add HSTS/redirect                        |
| Performance   | SELECT * used                                                            | Medium   | Limit fields in SELECT                   |

---

## **References**
- [OWASP Cheat Sheet: SQL Injection Prevention](https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html)
- [PHP Manual: htmlspecialchars](https://www.php.net/manual/en/function.htmlspecialchars.php)

---

*The above are critical review points. The fix suggestions are partial (pseudo-code/bare code lines) and should be considered before production deployment.*