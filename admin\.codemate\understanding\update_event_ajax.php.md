# High-Level Documentation: Event Update PHP Endpoint

## Overview

This script is a backend PHP endpoint designed to handle AJAX requests for updating event details in a database. It primarily serves administrative users in a web application (such as a CMS or event management system), allowing them to modify event information securely.

---

## Key Functionalities

1. **Session and Access Control**
   - Ensures a PHP session is started.
   - Enforces that the requester is an authenticated admin user via `requireAdmin()`.

2. **Dependencies**
   - Loads database connection and utility function files, including security/auth helpers.

3. **Request Handling**
   - Accepts only POST requests, typically from form submissions.

4. **CSRF Protection**
   - Verifies a Cross-Site Request Forgery (CSRF) token to prevent unauthorized form submissions.

5. **Event Existence Validation**
   - Retrieves the event by its `id` from the database to ensure the event exists before updating.

6. **Input Validation**
   - Extracts and sanitizes event data from the `$_POST` array.
   - Checks for required fields such as title, date, time, location, type, and price.
   - Ensures valid price and available ticket counts.

7. **Image Upload Handling**
   - Optionally processes and saves an uploaded event image to a server directory.
   - Verifies the file is an image and handles creation of the target directory.

8. **Error Handling**
   - Collects validation errors and returns them to the client if any are found.
   - Catches and logs database errors.

9. **Database Update**
   - Executes a SQL `UPDATE` statement to persist the new event details.
   - Updates only if all prior validations pass.

10. **Localized Messaging**
    - Provides a success message, optionally in the admin's preferred language if available.

11. **JSON Response**
    - Returns a JSON object containing a success flag, a user message, and the event ID if successful.

---

## Expected Input

- **POST fields**:
  - `event_id`: Numeric ID of the event to update.
  - `csrf_token`: Security token for CSRF protection.
  - `title`, `description`, `date`, `time`, `location`, `price`, `original_price`, `type`, `available_tickets`, `is_featured`.
  - `image` (optional): File upload field with a (new) event image.

---

## Example Output

On success:

```json
{
  "success": true,
  "message": "Event updated successfully",
  "event_id": 42
}
```

On error:

```json
{
  "success": false,
  "message": "Event title is required<br>Event price must be greater than 0"
}
```

---

## Security Checks

- Session and admin enforcement.
- CSRF token mandatory for POST.
- Input validation and sanitization.
- Validates uploaded file is an image.
- Database errors are logged and not disclosed fully to the user.

---

## Primary Use Cases

- An administrator submits an ‘edit event’ form in an admin dashboard.
- AJAX front-end calls this endpoint to save changes to an existing event.

---

## Extensibility & Notes

- Can be adapted for additional form fields or validation.
- Language/message file integration is supported for localization.
- Error handling is robust; issues are returned as user-friendly messages and logged for debugging.

---

**In summary**:  
This code provides a secure, admin-only backend endpoint for editing event details in a database, including support for file uploads and multi-language success messaging, all wrapped in robust validation and error handling.