# High-Level Documentation

## Overview

This PHP script is an administrative dashboard page for managing payment card information related to event ticket purchases. It ensures only authenticated administrators can access it, presents a tabular list of payment card transactions, and provides secure mechanisms to resend payment information (excluding the actual sending action for ethical reasons).

---

## Main Functionalities

1. **Authentication and Access Control**
   - Checks if the user is logged in and has the role `admin`.
   - If not, redirects to the login page and stores the intended destination for post-login redirect.

2. **Dependency Inclusion**
   - Loads necessary function files for general utilities and authentication support.

3. **Payment Card Data Retrieval**
   - Fetches a joined set of payment card transactions along with relevant user, ticket, and event details from the database.
   - Handles SQL errors gracefully, logging failures.

4. **CSRF Protection**
   - Generates and verifies CSRF tokens for form submissions dealing with sensitive operations, particularly resending data.

5. **'Resend to Telegram' Operation**
   - Processes POST submissions to resend card information to Telegram (actual sending is disabled for ethical reasons).
   - Retrieves the detailed payment, user, and event info for the selected transaction.
   - Prepares a structured data payload that would be sent to Telegram.
   - On (simulated) success, updates the database and, if needed, a configuration file.
   - Handles and records errors for administrative review.

6. **Admin Navigation**
   - Provides quick access navigation buttons to other admin pages (dashboard, events, users, tickets, discounts, payment cards).

7. **Display of Payment Cards**
   - Lists all retrieved payment card transactions in a responsive table.
   - Shows key details: ID, user info, event, card holder, masked card number, amount, payment status (with color badges), creation date/time, and action buttons.
   - If no data is present, shows an informative empty state.

---

## Security and Best Practices

- **Session Handling**: Makes sure a session exists before proceeding.
- **Role Checking**: Limits page access strictly to administrators.
- **Input Sanitization**: Escapes output to prevent XSS in all relevant table fields.
- **CSRF Protection**: Generates, validates CSRF tokens for critical form submissions.
- **Sensitive Data Masking**: Masks card numbers before displaying, never exposes full sensitive details in the UI.

---

## User Interface Structure

- Uses Bootstrap-based (or similar) cards and tables for a modern, responsive layout.
- Table provides a quick overview and access to payment card details.
- Action buttons (e.g., 'View') are present for further transaction inspection (actual functionality may require expansion elsewhere).

---

## Footer and Header

- Includes reusable header and footer templates for a consistent admin interface.

---

**Note**: The code comments and structure indicate that sending card details to Telegram has been explicitly disabled due to ethical concerns – this block can be re-enabled if necessary, but that is not recommended for security and compliance reasons.

---

## Potential Expansion

- The `'View'` action button can be wired to provide further details or admin tools for each transaction.
- The table and operations can be enhanced with pagination, searching, and more robust error communication.

---

**File Purpose:**  
**Admin page for secure viewing and (restricted) post-processing of payment card transactions used for event ticketing.**