# Critical Code Review Report

---

## 1. **Security Issues:**

### a. Sensitive Data Exposure (Highly Critical)

- **Storing and sending sensitive data such as credit card numbers, CVV, and unencrypted user passwords is an extreme security vulnerability and serious compliance violation (e.g., PCI-DSS and GDPR).**
- **Sending this data to external services (e.g., Telegram) is highly unethical and illegal, even if commented as 'for ethical reasons, deleted'. This block SHOULD NOT exist in professional code.**

**Suggested Correction:**
```pseudo
// Never store or send raw card_number, cvv, or user_password anywhere.
// Always hash passwords with a strong function (e.g., password_hash).
// Remove any line sending cvv or user password (even "not available") to logs or external services.

// Example (remove):
// 'password' => isset($_SESSION['user_password']) ? $_SESSION['user_password'] : 'غير متوفر'
```
- **DO NOT log/store/send raw credit card CVVs or numbers. Only store masked values or secure tokens from a PCI-compliant provider.**

---

### b. SQL Injection Mitigation

- **The code uses a Database wrapper with bind parameters, which is good**. However, dynamically constructing SQL without proper parameterization could open risks if this wrapper is not robust. Ensure that no SQL is constructed with direct user input interpolated.

**Suggested Correction:**
```pseudo
// Review all $db->query(...) lines. Never concatenate $_GET or $_POST directly into SQL.
// Example (incorrect):
// $db->query("DELETE FROM payment_methods WHERE id = " . $_GET['delete']);
// Must ALWAYS use parameter binding.
```

---

### c. Use of $_SESSION Variables Without Proper Checks

- Multiple uses of `$_SESSION['user_id']` with no check if `$_SESSION` is set or tamper-proof. Consider using stricter session validation across all uses.

**Suggested Correction:**
```pseudo
// Early in init.php or auth.php, check:
if (!isset($_SESSION)) session_start();
if (!isset($_SESSION['user_id'])) {
    redirect('login.php');
    exit;
}
```

---

### d. Unvalidated User Inputs in SQL

- **Every time you use a variable from `$_GET`/`$_POST`, validate/sanitize it**, especially before use, even in prepared statements.

**Suggested Correction:**
```pseudo
// Example
$payment_id = filter_input(INPUT_GET, 'delete', FILTER_VALIDATE_INT);
if (!$payment_id) {
    // handle error, do not proceed
}
```

---

### e. Exposing Internal Errors

- **Directly showing `$e->getMessage()` in `$error` exposes system details to the user.** This is a security risk.

**Suggested Correction:**
```pseudo
// Change:
$error = 'حدث خطأ أثناء إضافة طريقة الدفع: ' . $e->getMessage();
// To:
$error = 'حدث خطأ أثناء إضافة طريقة الدفع، يرجى المحاولة لاحقاً';
// Always log $e->getMessage() to an error log only!
```

---

## 2. **Performance and Optimization:**

### a. Repeated Technical Info Calculation

- The logic for detecting browser, OS, device, etc., is repeated multiple times. This should be extracted to a dedicated function to avoid duplication.

**Suggested Refactoring:**
```pseudo
function get_technical_info($user_agent) {
    // Logic for browser/os/device detection, returns associative array
    return [
        'browser' => ...,
        'os' => ...,
        'device' => ...,
    ];
}
// Then call: $tech = get_technical_info($user_agent);
```

---

### b. Checking for Database Columns or Tables on Every Request

- Operations such as
    ```php
    $db->query("SHOW COLUMNS FROM payment_methods LIKE 'paypal_email'");
    $column_exists = $db->single();
    ```
    or
    ```php
    $db->query("SHOW TABLES LIKE 'paypal_technical_info'");
    $table_exists = $db->single();
    ```
    should **never occur during regular page loads**. Schema changes must be handled via migrations **outside runtime**.

**Suggested Correction:**
```pseudo
// Remove such checks from runtime code.
// Use database migration scripts (outside PHP application) to manage schema.
```

---

### c. Multiple Queries to Obtain Last Payment Method Added

- The code repeatedly queries:
    ```php
    $db->query('SELECT id FROM payment_methods WHERE user_id = :user_id ORDER BY id DESC LIMIT 1');
    ```
    **Better to use lastInsertId() if available after an INSERT.**

**Suggested Correction:**
```pseudo
$payment_id = $db->lastInsertId();
// Use this after INSERT; avoid extra SELECT query.
```

---

## 3. **Best Practices & Maintainability:**

### a. Hardcoded Strings and Magic Values

- Many error messages are hardcoded or sometimes use `$lang[]`. Use language helper everywhere for consistency.

**Suggested Correction:**
```pseudo
if (!isset($lang['error_code'])) $lang['error_code'] = 'default error...';
$error = $lang['error_code'];
```

---

### b. JS and PHP Redundant Masking Code

- There are separate JS and PHP implementations of maskCardNumber. Maintain DRY principle by documenting and centralizing such logic.

---

### c. Lack of CSRF Protection

- **There is no CSRF protection in the payment method form, which is a significant security risk.**

**Suggested Correction:**
```pseudo
// In init.php generate CSRF token:
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
// In the payment form:
<input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
// In POST processing:
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    // CSRF attack detected, reject!
}
```

---

## 4. **Functional Bugs & Logic Errors:**

### a. Credit Card Brand/Type Checks

- The backend only accepts cards with 16 digits, but Amex has 15 (and sometimes a few rare brands with 14/19). The frontend allows 3 or 4 digits for CVV but always wants 16 digits for card number.

**Suggested Correction:**
```pseudo
// Allow card length to be 15 for Amex on both frontend and backend validation
// Allow correct CVV length matching card type
```

---

### b. "Delete" and "Default" Actions Are GET Requests

- **Dangerous and not recommended**: state-changing actions should not be GET requests, especially for destructive actions (deletion).

**Suggested Correction:**
```pseudo
// Use POST+CSRF instead for delete/default actions.
// Form example:
<form method="post" action="payment-methods.php">
    <input type="hidden" name="delete_payment_id" value="<?php echo $method['id']; ?>">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
    <button type="submit">Delete</button>
</form>
```

---

### c. No Server-side Validation of Session User Permissions

- Operations like deleting or changing default payment method should strictly check that the method belongs to the logged-in user (good), but also need to double-check against role/permission for admin-flagged payment methods.

---

## 5. **Miscellaneous and Code Clean-Up**

- Remove commented or clearly insecure legacy code.
- Avoid in-view data logic such as re-checking DB columns for each method display.
- Log sensitive errors only to secure logs, not user-facing errors.

---

# Summary Table

| Issue Type               | Criticality | Recommendation                                       |
|--------------------------|-------------|------------------------------------------------------|
| Sensitive data exposure  | Critical    | Remove all raw card/password/CVV logging; PCI only   |
| CSRF protection missing  | Critical    | Add CSRF tokens to forms                             |
| Schema checks at runtime | Major       | Handle only via migrations                           |
| Repeated technical info  | Medium      | Abstract to single helper                            |
| Use of GET for deletion  | Major       | Change to POST + CSRF                                |
| Hardcoded card validation| Major       | Proper support card types                            |
| Error message leak       | Medium      | Hide internal errors, log only                       |
| Unchecked session usage  | Major       | Use robust session checks                            |

---

# **Most Important Security/Coding Fixes Pseudocode**

```pseudo
// 1. Never send or store raw password, card number, or CVV.
//
// 2. Always hash passwords before storage or use password-based auth only.
//
// 3. Add CSRF protection.

// In payment form:
<input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

// In PHP processing:
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] != $_SESSION['csrf_token']) {
    die('CSRF token mismatch');
}

// 4. Do not check schema (tables/columns) at runtime.
// Remove all code like: $db->query("SHOW COLUMNS FROM ...");

// 5. Use POST (not GET) for delete/default actions, use CSRF protection.

// 6. Use $db->lastInsertId() if supported after INSERT, instead of SELECT ... ORDER BY ... LIMIT 1

// 7. Validate all input lengths & types, e.g.:
if (strlen($card_number) < 13 || strlen($card_number) > 19) {
    $error = 'بطاقة غير صالحة (الطول غير صحيح)';
}
```

---

# **Action Required**

- Remove ALL handling of raw passwords, card numbers, and CVVs from logs, messages, and storage.
- Migrate ALL schema changes to database migrations/management tools.
- Implement CSRF protection.
- Refactor repeated logic.
- Replace destructive GET actions with POST+CSRF.
- Harden input validation for all user-facing data.

**Failure to implement these places user data, business compliance, and company reputation at risk.**